{"name": "space-game-server", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "npm run proto:build && tsc", "proto:build": "pbjs -t static-module -w commonjs -o src/proto/generated/game_messages.js src/proto/game_messages.proto && pbts -o src/proto/generated/game_messages.d.ts src/proto/generated/game_messages.js", "proto:clean": "rm -rf src/proto/generated", "node:center": "cross-env START_MODE=center-node ts-node -r tsconfig-paths/register src/index.ts", "node:gate": "cross-env START_MODE=gateway ts-node -r tsconfig-paths/register src/index.ts", "node:room": "cross-env START_MODE=room-node ts-node -r tsconfig-paths/register src/index.ts", "node:web": "cross-env START_MODE=web-node ts-node -r tsconfig-paths/register src/index.ts", "node:game": "cross-env START_MODE=game-node PORT=$npm_config_port PARAMS=$npm_config_params ts-node -r tsconfig-paths/register src/index.ts", "dev": "ts-node -r tsconfig-paths/register src/index.ts", "test:protobuf": "node scripts/test-protobuf.js"}, "dependencies": {"@socket.io/redis-adapter": "^8.3.0", "@types/protobufjs": "^6.0.0", "dotenv": "^16.5.0", "execa": "^9.5.2", "express": "^4.17.1", "ioredis": "^5.6.1", "protobufjs": "^7.5.3", "redis": "^3.1.2", "reflect-metadata": "^0.2.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/express": "^4.17.8", "@types/node": "^14.18.63", "@types/redis": "^2.8.29", "@types/socket.io": "^3.0.0", "cross-env": "^7.0.3", "nodemon": "^3.1.9", "protobufjs-cli": "^1.2.0", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^4.9.5"}}