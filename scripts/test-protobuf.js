#!/usr/bin/env node

/**
 * Protobuf测试脚本
 * 用于测试gateway节点的protobuf通讯功能
 */

const io = require('socket.io-client');

// 测试配置
const GATEWAY_URL = 'http://localhost:3001'; // gateway节点地址
const TEST_BTC_ADDRESS = 'test-btc-address-' + Date.now();
const TEST_SESSION_ID = 'test-session-' + Date.now();

console.log('开始测试protobuf通讯...');

// 创建客户端连接（默认使用protobuf）
const protobufClient = io(GATEWAY_URL);

// 创建第二个客户端连接（对照组）
const jsonClient = io(GATEWAY_URL);

let protobufMessagesReceived = 0;
let jsonMessagesReceived = 0;

// Protobuf客户端事件处理
protobufClient.on('connect', () => {
  console.log('✅ Protobuf客户端已连接:', protobufClient.id);

  // 登录
  protobufClient.emit('player_login', {
    btcAddress: TEST_BTC_ADDRESS + '-protobuf',
    sessionId: TEST_SESSION_ID + '-protobuf'
  }, (success) => {
    if (success) {
      console.log('✅ Protobuf客户端登录成功');
      startProtobufTests();
    } else {
      console.log('❌ Protobuf客户端登录失败');
    }
  });
});

// JSON客户端事件处理
jsonClient.on('connect', () => {
  console.log('✅ JSON客户端已连接:', jsonClient.id);

  // 登录
  jsonClient.emit('player_login', {
    btcAddress: TEST_BTC_ADDRESS + '-json',
    sessionId: TEST_SESSION_ID + '-json'
  }, (success) => {
    if (success) {
      console.log('✅ JSON客户端登录成功');
      startJsonTests();
    } else {
      console.log('❌ JSON客户端登录失败');
    }
  });
});

// 监听protobuf消息
protobufClient.on('player_notice_protobuf', (binaryData) => {
  protobufMessagesReceived++;
  console.log(`📦 收到protobuf消息 #${protobufMessagesReceived}, 大小: ${binaryData.length} bytes`);

  // 这里应该使用protobuf.js解码消息，但为了简化测试，我们只记录收到的消息
  // const gameMessage = GameMessage.decode(binaryData);
});

// 监听JSON消息
protobufClient.on('player_notice', (jsonData) => {
  console.log('📄 Protobuf客户端收到JSON消息:', jsonData.pid);
});

jsonClient.on('player_notice', (jsonData) => {
  jsonMessagesReceived++;
  console.log(`📄 收到JSON消息 #${jsonMessagesReceived}, 类型: ${jsonData.pid}`);
});

// 错误处理
protobufClient.on('error', (error) => {
  console.error('❌ Protobuf客户端错误:', error);
});

jsonClient.on('error', (error) => {
  console.error('❌ JSON客户端错误:', error);
});

// 断开连接处理
protobufClient.on('disconnect', () => {
  console.log('🔌 Protobuf客户端已断开连接');
});

jsonClient.on('disconnect', () => {
  console.log('🔌 JSON客户端已断开连接');
});

// 开始protobuf测试
function startProtobufTests() {
  console.log('\n🧪 开始Protobuf功能测试...');

  // 测试心跳
  setTimeout(() => {
    protobufClient.emit('player_heartbeat', {
      lastPing: Date.now()
    }, (response) => {
      console.log('💓 Protobuf客户端心跳响应:', response);
    });
  }, 1000);

  // 测试获取房间列表
  setTimeout(() => {
    protobufClient.emit('get-room-list', {
      page: 1,
      limit: 10
    }, (rooms) => {
      console.log('🏠 Protobuf客户端房间列表:', rooms ? rooms.length : 0, '个房间');
    });
  }, 2000);
}

// 开始JSON测试
function startJsonTests() {
  console.log('\n🧪 开始JSON功能测试...');

  // 测试心跳
  setTimeout(() => {
    jsonClient.emit('player_heartbeat', {
      lastPing: Date.now()
    }, (response) => {
      console.log('💓 JSON客户端心跳响应:', response);
    });
  }, 1000);

  // 测试获取房间列表
  setTimeout(() => {
    jsonClient.emit('get-room-list', {
      page: 1,
      limit: 10
    }, (rooms) => {
      console.log('🏠 JSON客户端房间列表:', rooms ? rooms.length : 0, '个房间');
    });
  }, 2000);
}

// 测试结果统计
setTimeout(() => {
  console.log('\n📊 测试结果统计:');
  console.log(`Protobuf消息接收数量: ${protobufMessagesReceived}`);
  console.log(`JSON消息接收数量: ${jsonMessagesReceived}`);

  if (protobufMessagesReceived > 0) {
    console.log('✅ Protobuf通讯测试成功！');
  } else {
    console.log('⚠️  未收到protobuf消息，可能需要检查配置');
  }

  // 断开连接
  protobufClient.disconnect();
  jsonClient.disconnect();

  setTimeout(() => {
    process.exit(0);
  }, 1000);
}, 10000);

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n👋 测试中断，正在清理...');
  protobufClient.disconnect();
  jsonClient.disconnect();
  process.exit(0);
});

console.log('⏳ 等待连接建立...');
