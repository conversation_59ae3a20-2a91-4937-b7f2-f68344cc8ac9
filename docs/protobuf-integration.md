# Protobuf协议集成说明

本项目已集成protobuf协议，专门用于gateway节点与客户端的通讯。protobuf提供了更高效的二进制序列化，相比JSON具有更小的数据包大小和更快的序列化/反序列化速度。

## 功能特性

- **向后兼容**: 默认情况下仍使用JSON格式，不会影响现有客户端
- **渐进式迁移**: 可以逐步将客户端迁移到protobuf协议
- **智能回退**: 如果protobuf编码失败，自动回退到JSON格式
- **灵活配置**: 支持通过环境变量和运行时配置控制protobuf的使用

## 架构设计

### 核心组件

1. **ProtobufHandler**: 负责protobuf消息的编码和解码
2. **ProtobufConfig**: 管理protobuf配置和客户端支持状态
3. **GatewayClient**: 修改后的客户端类，支持protobuf通讯
4. **消息定义**: 在`src/proto/game_messages.proto`中定义的protobuf消息格式

### 消息流程

```
客户端 -> Gateway节点 -> 检查protobuf支持 -> 选择协议 -> 发送消息
```

## 配置说明

### 环境变量配置

在`.env`文件中配置以下变量：

```bash
# 启用protobuf协议
ENABLE_PROTOBUF=true

# 支持protobuf的客户端（用逗号分隔）
PROTOBUF_SUPPORTED_CLIENTS=*************,1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa

# 强制使用protobuf的消息类型
PROTOBUF_FORCED_TYPES=10,11,12
```

### 运行时配置

```typescript
import { ProtobufConfig } from 'proto/protobuf-config';

const config = ProtobufConfig.getInstance();

// 启用protobuf
config.setEnabled(true);

// 添加支持protobuf的客户端
config.addSupportedClient('client-id');

// 强制特定消息类型使用protobuf
config.addForcedProtobufType(S2C_PacketTypes.PlayerPosition);
```

## 客户端集成

### 检测protobuf支持

客户端需要在连接时声明protobuf支持：

```javascript
// 方式1：通过查询参数
const socket = io('ws://localhost:3000', {
  query: {
    protobuf: 'true'
  }
});

// 方式2：通过User-Agent
const socket = io('ws://localhost:3000', {
  extraHeaders: {
    'User-Agent': 'MyClient/1.0 protobuf-enabled'
  }
});
```

### 接收protobuf消息

```javascript
// 接收protobuf格式的消息
socket.on('player_notice_protobuf', (binaryData) => {
  // 使用protobuf.js解码消息
  const gameMessage = GameMessage.decode(binaryData);
  const messageType = gameMessage.packetType;
  const data = decodeSpecificMessage(gameMessage.data, messageType);
  
  // 处理解码后的数据
  handleGameMessage(messageType, data);
});

// 仍然支持JSON格式（向后兼容）
socket.on('player_notice', (jsonData) => {
  handleGameMessage(jsonData.pid, jsonData);
});
```

## 支持的消息类型

当前支持以下消息类型的protobuf编码：

- `PlayerEnter` (1): 玩家进入
- `PlayerLeave` (2): 玩家离开  
- `PlayerPosition` (10): 玩家位置
- `PlayerAnimation` (11): 玩家动画
- `PlayerUpdate` (12): 玩家更新
- `PetPosition` (50): 宠物位置
- `PetAnimation` (51): 宠物动画
- `ChatEnter` (100): 聊天进入
- `ChatLeave` (101): 聊天离开
- `ChatMessage` (102): 聊天消息
- `RedPacketUpdate` (201): 红包更新

## 开发指南

### 添加新的消息类型

1. 在`src/proto/game_messages.proto`中定义新的消息结构
2. 运行`npm run proto:build`重新生成TypeScript定义
3. 在`ProtobufHandler`中添加消息类型映射
4. 更新相关的枚举映射

### 调试protobuf

启用调试日志：

```typescript
// 在开发环境中启用详细日志
console.debug(`Sent protobuf message: ${S2C_PacketTypes[pid]} to ${clientId}`);
```

### 性能监控

可以通过以下方式监控protobuf的使用情况：

```typescript
const config = ProtobufConfig.getInstance();
const status = config.getStatus();
console.log('Protobuf status:', status);
```

## 最佳实践

1. **渐进式迁移**: 先在测试环境启用protobuf，逐步迁移生产环境
2. **监控性能**: 对比protobuf和JSON的性能差异
3. **错误处理**: 确保protobuf编码失败时能正确回退到JSON
4. **版本兼容**: 保持protobuf消息定义的向后兼容性

## 故障排除

### 常见问题

1. **protobuf编码失败**: 检查数据格式是否符合proto定义
2. **客户端无法解码**: 确保客户端使用相同版本的proto文件
3. **性能问题**: 检查是否有大量的编码/解码操作

### 日志分析

查看相关日志：

```bash
# 查看protobuf相关日志
grep "protobuf" logs/gateway.log

# 查看编码错误
grep "Failed to encode protobuf" logs/gateway.log
```

## 未来扩展

- 支持更多消息类型
- 添加消息压缩
- 实现消息版本控制
- 支持流式protobuf消息
