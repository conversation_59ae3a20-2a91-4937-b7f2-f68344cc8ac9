# 客户端Protobuf使用指南

本文档说明如何在客户端使用protobuf协议与gateway节点进行通讯，特别是发送`PLAYER_ACTION`等数据。

## 概述

现在gateway节点支持接收和解析来自客户端的protobuf数据，包括：
- 玩家动作数据 (`PLAYER_ACTION`)
- 玩家请求数据 (`PLAYER_REQUEST`)
- 玩家聊天数据 (`PLAYER_CHAT`)

## 客户端实现

### 1. 安装protobuf.js

```bash
npm install protobufjs
```

### 2. 获取protobuf定义文件

将服务器的`src/proto/game_messages.proto`文件复制到客户端项目中。

### 3. 生成客户端protobuf代码

```bash
# 生成JavaScript代码
pbjs -t static-module -w commonjs -o game_messages.js game_messages.proto

# 生成TypeScript定义（如果使用TypeScript）
pbts -o game_messages.d.ts game_messages.js
```

### 4. 客户端编码示例

#### JavaScript示例

```javascript
const protobuf = require('protobufjs');
const io = require('socket.io-client');

// 加载protobuf定义
const root = protobuf.loadSync('game_messages.proto');
const PlayerActionMessage = root.lookupType('game.PlayerActionMessage');
const ClientPlayerPosition = root.lookupType('game.ClientPlayerPosition');
const ClientPlayerAnimation = root.lookupType('game.ClientPlayerAnimation');

// 连接到服务器
const socket = io('ws://localhost:3001');

// 编码玩家位置数据
function sendPlayerPosition(x, y, z, rotationX, rotationY, rotationZ, rotationW) {
  // 1. 编码内部位置数据
  const positionData = ClientPlayerPosition.create({
    x: x,
    y: y,
    z: z,
    rotationX: rotationX,
    rotationY: rotationY,
    rotationZ: rotationZ,
    rotationW: rotationW
  });
  const positionBuffer = ClientPlayerPosition.encode(positionData).finish();
  
  // 2. 编码动作消息包装器
  const actionMessage = PlayerActionMessage.create({
    pid: 10, // C2S_PacketTypes.PlayerPosition
    actionData: positionBuffer,
    btcAddress: 'your-btc-address',
    timestamp: Date.now()
  });
  
  // 3. 发送protobuf数据
  const messageBuffer = PlayerActionMessage.encode(actionMessage).finish();
  socket.emit('player_action_protobuf', messageBuffer);
}

// 编码玩家动画数据
function sendPlayerAnimation(animationName, speed = 1.0, loop = false) {
  // 1. 编码内部动画数据
  const animationData = ClientPlayerAnimation.create({
    curAnimation: animationName,
    speed: speed,
    loop: loop
  });
  const animationBuffer = ClientPlayerAnimation.encode(animationData).finish();
  
  // 2. 编码动作消息包装器
  const actionMessage = PlayerActionMessage.create({
    pid: 11, // C2S_PacketTypes.PlayerAnimation
    actionData: animationBuffer,
    btcAddress: 'your-btc-address',
    timestamp: Date.now()
  });
  
  // 3. 发送protobuf数据
  const messageBuffer = PlayerActionMessage.encode(actionMessage).finish();
  socket.emit('player_action_protobuf', messageBuffer);
}

// 使用示例
socket.on('connect', () => {
  console.log('Connected to server');
  
  // 登录
  socket.emit('player_login', {
    btcAddress: 'your-btc-address',
    sessionId: 'your-session-id'
  }, (success) => {
    if (success) {
      // 发送位置数据
      sendPlayerPosition(100, 200, 150, 0, 0.707, 0, 0.707);
      
      // 发送动画数据
      setTimeout(() => {
        sendPlayerAnimation('walk', 1.0, true);
      }, 1000);
    }
  });
});
```

#### TypeScript示例

```typescript
import * as io from 'socket.io-client';
import { game } from './game_messages'; // 生成的protobuf定义

class GameClient {
  private socket: SocketIOClient.Socket;
  private btcAddress: string;

  constructor(serverUrl: string, btcAddress: string) {
    this.socket = io(serverUrl);
    this.btcAddress = btcAddress;
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    this.socket.on('connect', () => {
      console.log('Connected to server');
      this.login();
    });

    // 监听服务器的protobuf响应
    this.socket.on('player_notice_protobuf', (binaryData: ArrayBuffer) => {
      try {
        const gameMessage = game.GameMessage.decode(new Uint8Array(binaryData));
        console.log('Received protobuf message:', gameMessage);
      } catch (error) {
        console.error('Error decoding protobuf message:', error);
      }
    });
  }

  private login(): void {
    this.socket.emit('player_login', {
      btcAddress: this.btcAddress,
      sessionId: 'session-' + Date.now()
    }, (success: boolean) => {
      if (success) {
        console.log('Login successful');
        this.startGameLoop();
      }
    });
  }

  public sendPlayerPosition(x: number, y: number, z: number, 
                           rotationX: number, rotationY: number, 
                           rotationZ: number, rotationW: number): void {
    try {
      // 编码位置数据
      const positionData = game.ClientPlayerPosition.create({
        x, y, z, rotationX, rotationY, rotationZ, rotationW
      });
      const positionBuffer = game.ClientPlayerPosition.encode(positionData).finish();

      // 编码动作消息
      const actionMessage = game.PlayerActionMessage.create({
        pid: 10, // C2S_PacketTypes.PlayerPosition
        actionData: positionBuffer,
        btcAddress: this.btcAddress,
        timestamp: Date.now()
      });

      // 发送
      const messageBuffer = game.PlayerActionMessage.encode(actionMessage).finish();
      this.socket.emit('player_action_protobuf', messageBuffer);
    } catch (error) {
      console.error('Error sending player position:', error);
    }
  }

  public sendPlayerAnimation(animationName: string, speed: number = 1.0, loop: boolean = false): void {
    try {
      // 编码动画数据
      const animationData = game.ClientPlayerAnimation.create({
        curAnimation: animationName,
        speed,
        loop
      });
      const animationBuffer = game.ClientPlayerAnimation.encode(animationData).finish();

      // 编码动作消息
      const actionMessage = game.PlayerActionMessage.create({
        pid: 11, // C2S_PacketTypes.PlayerAnimation
        actionData: animationBuffer,
        btcAddress: this.btcAddress,
        timestamp: Date.now()
      });

      // 发送
      const messageBuffer = game.PlayerActionMessage.encode(actionMessage).finish();
      this.socket.emit('player_action_protobuf', messageBuffer);
    } catch (error) {
      console.error('Error sending player animation:', error);
    }
  }

  private startGameLoop(): void {
    // 模拟游戏循环，定期发送位置数据
    setInterval(() => {
      const x = Math.random() * 1000;
      const y = Math.random() * 1000;
      const z = Math.random() * 1000;
      this.sendPlayerPosition(x, y, z, 0, 0, 0, 1);
    }, 100); // 每100ms发送一次位置数据
  }
}

// 使用
const client = new GameClient('ws://localhost:3001', 'your-btc-address');
```

## 支持的消息类型

### 玩家动作消息 (player_action_protobuf)

| 消息类型 | PID | 数据结构 | 说明 |
|---------|-----|----------|------|
| PlayerEnter | 1 | ClientPlayerEnter | 玩家进入，包含头像数据 |
| PlayerLeave | 2 | 无 | 玩家离开 |
| PlayerPosition | 10 | ClientPlayerPosition | 玩家位置和旋转 |
| PlayerAnimation | 11 | ClientPlayerAnimation | 玩家动画 |
| PlayerUpdate | 12 | ClientPlayerUpdate | 玩家状态更新 |

### 玩家请求消息 (player_request_protobuf)

| 请求类型 | PID | 数据格式 | 说明 |
|---------|-----|----------|------|
| PickUpRedPacket | 1 | JSON字符串 | 拾取红包请求 |

### 玩家聊天消息 (player_chat_protobuf)

| 消息类型 | PID | 数据结构 | 说明 |
|---------|-----|----------|------|
| ChatEnter | 100 | 无 | 进入聊天 |
| ChatLeave | 101 | 无 | 离开聊天 |
| ChatMessage | 102 | ChatMessage | 聊天消息 |

## 性能优势

使用protobuf相比JSON的优势：

1. **数据包大小**：通常减少20-50%
2. **序列化速度**：更快的编码/解码
3. **类型安全**：编译时检查
4. **向前兼容**：支持协议演进

## 错误处理

服务器会自动处理protobuf解析错误：

1. **解析失败**：服务器会记录错误日志，并可能发送错误消息给客户端
2. **地址不匹配**：如果消息中的btcAddress与登录地址不匹配，消息会被拒绝
3. **自动回退**：如果protobuf处理失败，服务器仍支持JSON格式

## 调试技巧

1. **启用调试日志**：服务器会输出protobuf消息接收日志
2. **数据验证**：确保protobuf数据结构正确
3. **网络监控**：使用浏览器开发者工具监控WebSocket流量

## 最佳实践

1. **批量发送**：对于频繁的位置更新，考虑批量发送
2. **压缩**：对于大型消息，可以考虑额外的压缩
3. **错误重试**：实现适当的错误处理和重试机制
4. **版本兼容**：保持protobuf定义的向前兼容性
