/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
"use strict";

var $protobuf = require("protobufjs/minimal");

// Common aliases
var $Reader = $protobuf.Reader, $Writer = $protobuf.Writer, $util = $protobuf.util;

// Exported root namespace
var $root = $protobuf.roots["default"] || ($protobuf.roots["default"] = {});

$root.game = (function() {

    /**
     * Namespace game.
     * @exports game
     * @namespace
     */
    var game = {};

    /**
     * PacketType enum.
     * @name game.PacketType
     * @enum {number}
     * @property {number} PLAYER_ENTER=1 PLAYER_ENTER value
     * @property {number} PLAYER_LEAVE=2 PLAYER_LEAVE value
     * @property {number} PLAYER_POSITION=10 PLAYER_POSITION value
     * @property {number} PLAYER_ANIMATION=11 PLAYER_ANIMATION value
     * @property {number} PLAYER_UPDATE=12 PLAYER_UPDATE value
     * @property {number} PET_POSITION=50 PET_POSITION value
     * @property {number} PET_ANIMATION=51 PET_ANIMATION value
     * @property {number} CHAT_ENTER=100 CHAT_ENTER value
     * @property {number} CHAT_LEAVE=101 CHAT_LEAVE value
     * @property {number} CHAT_MESSAGE=102 CHAT_MESSAGE value
     * @property {number} RED_PACKET_UPDATE=201 RED_PACKET_UPDATE value
     * @property {number} RED_PACKET_REWARD=202 RED_PACKET_REWARD value
     */
    game.PacketType = (function() {
        var valuesById = {}, values = Object.create(valuesById);
        values[valuesById[1] = "PLAYER_ENTER"] = 1;
        values[valuesById[2] = "PLAYER_LEAVE"] = 2;
        values[valuesById[10] = "PLAYER_POSITION"] = 10;
        values[valuesById[11] = "PLAYER_ANIMATION"] = 11;
        values[valuesById[12] = "PLAYER_UPDATE"] = 12;
        values[valuesById[50] = "PET_POSITION"] = 50;
        values[valuesById[51] = "PET_ANIMATION"] = 51;
        values[valuesById[100] = "CHAT_ENTER"] = 100;
        values[valuesById[101] = "CHAT_LEAVE"] = 101;
        values[valuesById[102] = "CHAT_MESSAGE"] = 102;
        values[valuesById[201] = "RED_PACKET_UPDATE"] = 201;
        values[valuesById[202] = "RED_PACKET_REWARD"] = 202;
        return values;
    })();

    game.GameMessage = (function() {

        /**
         * Properties of a GameMessage.
         * @memberof game
         * @interface IGameMessage
         * @property {game.PacketType|null} [packetType] GameMessage packetType
         * @property {Uint8Array|null} [data] GameMessage data
         * @property {number|Long|null} [timestamp] GameMessage timestamp
         */

        /**
         * Constructs a new GameMessage.
         * @memberof game
         * @classdesc Represents a GameMessage.
         * @implements IGameMessage
         * @constructor
         * @param {game.IGameMessage=} [properties] Properties to set
         */
        function GameMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * GameMessage packetType.
         * @member {game.PacketType} packetType
         * @memberof game.GameMessage
         * @instance
         */
        GameMessage.prototype.packetType = 1;

        /**
         * GameMessage data.
         * @member {Uint8Array} data
         * @memberof game.GameMessage
         * @instance
         */
        GameMessage.prototype.data = $util.newBuffer([]);

        /**
         * GameMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof game.GameMessage
         * @instance
         */
        GameMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new GameMessage instance using the specified properties.
         * @function create
         * @memberof game.GameMessage
         * @static
         * @param {game.IGameMessage=} [properties] Properties to set
         * @returns {game.GameMessage} GameMessage instance
         */
        GameMessage.create = function create(properties) {
            return new GameMessage(properties);
        };

        /**
         * Encodes the specified GameMessage message. Does not implicitly {@link game.GameMessage.verify|verify} messages.
         * @function encode
         * @memberof game.GameMessage
         * @static
         * @param {game.IGameMessage} message GameMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        GameMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.packetType != null && Object.hasOwnProperty.call(message, "packetType"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.packetType);
            if (message.data != null && Object.hasOwnProperty.call(message, "data"))
                writer.uint32(/* id 2, wireType 2 =*/18).bytes(message.data);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 3, wireType 0 =*/24).int64(message.timestamp);
            return writer;
        };

        /**
         * Encodes the specified GameMessage message, length delimited. Does not implicitly {@link game.GameMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.GameMessage
         * @static
         * @param {game.IGameMessage} message GameMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        GameMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a GameMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.GameMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.GameMessage} GameMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        GameMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.GameMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.packetType = reader.int32();
                        break;
                    }
                case 2: {
                        message.data = reader.bytes();
                        break;
                    }
                case 3: {
                        message.timestamp = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a GameMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.GameMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.GameMessage} GameMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        GameMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a GameMessage message.
         * @function verify
         * @memberof game.GameMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        GameMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.packetType != null && message.hasOwnProperty("packetType"))
                switch (message.packetType) {
                default:
                    return "packetType: enum value expected";
                case 1:
                case 2:
                case 10:
                case 11:
                case 12:
                case 50:
                case 51:
                case 100:
                case 101:
                case 102:
                case 201:
                case 202:
                    break;
                }
            if (message.data != null && message.hasOwnProperty("data"))
                if (!(message.data && typeof message.data.length === "number" || $util.isString(message.data)))
                    return "data: buffer expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            return null;
        };

        /**
         * Creates a GameMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.GameMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.GameMessage} GameMessage
         */
        GameMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.GameMessage)
                return object;
            var message = new $root.game.GameMessage();
            switch (object.packetType) {
            default:
                if (typeof object.packetType === "number") {
                    message.packetType = object.packetType;
                    break;
                }
                break;
            case "PLAYER_ENTER":
            case 1:
                message.packetType = 1;
                break;
            case "PLAYER_LEAVE":
            case 2:
                message.packetType = 2;
                break;
            case "PLAYER_POSITION":
            case 10:
                message.packetType = 10;
                break;
            case "PLAYER_ANIMATION":
            case 11:
                message.packetType = 11;
                break;
            case "PLAYER_UPDATE":
            case 12:
                message.packetType = 12;
                break;
            case "PET_POSITION":
            case 50:
                message.packetType = 50;
                break;
            case "PET_ANIMATION":
            case 51:
                message.packetType = 51;
                break;
            case "CHAT_ENTER":
            case 100:
                message.packetType = 100;
                break;
            case "CHAT_LEAVE":
            case 101:
                message.packetType = 101;
                break;
            case "CHAT_MESSAGE":
            case 102:
                message.packetType = 102;
                break;
            case "RED_PACKET_UPDATE":
            case 201:
                message.packetType = 201;
                break;
            case "RED_PACKET_REWARD":
            case 202:
                message.packetType = 202;
                break;
            }
            if (object.data != null)
                if (typeof object.data === "string")
                    $util.base64.decode(object.data, message.data = $util.newBuffer($util.base64.length(object.data)), 0);
                else if (object.data.length >= 0)
                    message.data = object.data;
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a GameMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.GameMessage
         * @static
         * @param {game.GameMessage} message GameMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        GameMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.packetType = options.enums === String ? "PLAYER_ENTER" : 1;
                if (options.bytes === String)
                    object.data = "";
                else {
                    object.data = [];
                    if (options.bytes !== Array)
                        object.data = $util.newBuffer(object.data);
                }
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
            }
            if (message.packetType != null && message.hasOwnProperty("packetType"))
                object.packetType = options.enums === String ? $root.game.PacketType[message.packetType] === undefined ? message.packetType : $root.game.PacketType[message.packetType] : message.packetType;
            if (message.data != null && message.hasOwnProperty("data"))
                object.data = options.bytes === String ? $util.base64.encode(message.data, 0, message.data.length) : options.bytes === Array ? Array.prototype.slice.call(message.data) : message.data;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            return object;
        };

        /**
         * Converts this GameMessage to JSON.
         * @function toJSON
         * @memberof game.GameMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        GameMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for GameMessage
         * @function getTypeUrl
         * @memberof game.GameMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        GameMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.GameMessage";
        };

        return GameMessage;
    })();

    game.PlayerPosition = (function() {

        /**
         * Properties of a PlayerPosition.
         * @memberof game
         * @interface IPlayerPosition
         * @property {string|null} [btcAddress] PlayerPosition btcAddress
         * @property {number|null} [x] PlayerPosition x
         * @property {number|null} [y] PlayerPosition y
         * @property {number|null} [z] PlayerPosition z
         * @property {number|null} [rotationX] PlayerPosition rotationX
         * @property {number|null} [rotationY] PlayerPosition rotationY
         * @property {number|null} [rotationZ] PlayerPosition rotationZ
         * @property {number|null} [rotationW] PlayerPosition rotationW
         */

        /**
         * Constructs a new PlayerPosition.
         * @memberof game
         * @classdesc Represents a PlayerPosition.
         * @implements IPlayerPosition
         * @constructor
         * @param {game.IPlayerPosition=} [properties] Properties to set
         */
        function PlayerPosition(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerPosition btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.btcAddress = "";

        /**
         * PlayerPosition x.
         * @member {number} x
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.x = 0;

        /**
         * PlayerPosition y.
         * @member {number} y
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.y = 0;

        /**
         * PlayerPosition z.
         * @member {number} z
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.z = 0;

        /**
         * PlayerPosition rotationX.
         * @member {number} rotationX
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.rotationX = 0;

        /**
         * PlayerPosition rotationY.
         * @member {number} rotationY
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.rotationY = 0;

        /**
         * PlayerPosition rotationZ.
         * @member {number} rotationZ
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.rotationZ = 0;

        /**
         * PlayerPosition rotationW.
         * @member {number} rotationW
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.rotationW = 0;

        /**
         * Creates a new PlayerPosition instance using the specified properties.
         * @function create
         * @memberof game.PlayerPosition
         * @static
         * @param {game.IPlayerPosition=} [properties] Properties to set
         * @returns {game.PlayerPosition} PlayerPosition instance
         */
        PlayerPosition.create = function create(properties) {
            return new PlayerPosition(properties);
        };

        /**
         * Encodes the specified PlayerPosition message. Does not implicitly {@link game.PlayerPosition.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerPosition
         * @static
         * @param {game.IPlayerPosition} message PlayerPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerPosition.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            if (message.x != null && Object.hasOwnProperty.call(message, "x"))
                writer.uint32(/* id 2, wireType 5 =*/21).float(message.x);
            if (message.y != null && Object.hasOwnProperty.call(message, "y"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.y);
            if (message.z != null && Object.hasOwnProperty.call(message, "z"))
                writer.uint32(/* id 4, wireType 5 =*/37).float(message.z);
            if (message.rotationX != null && Object.hasOwnProperty.call(message, "rotationX"))
                writer.uint32(/* id 5, wireType 5 =*/45).float(message.rotationX);
            if (message.rotationY != null && Object.hasOwnProperty.call(message, "rotationY"))
                writer.uint32(/* id 6, wireType 5 =*/53).float(message.rotationY);
            if (message.rotationZ != null && Object.hasOwnProperty.call(message, "rotationZ"))
                writer.uint32(/* id 7, wireType 5 =*/61).float(message.rotationZ);
            if (message.rotationW != null && Object.hasOwnProperty.call(message, "rotationW"))
                writer.uint32(/* id 8, wireType 5 =*/69).float(message.rotationW);
            return writer;
        };

        /**
         * Encodes the specified PlayerPosition message, length delimited. Does not implicitly {@link game.PlayerPosition.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerPosition
         * @static
         * @param {game.IPlayerPosition} message PlayerPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerPosition.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerPosition message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerPosition} PlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerPosition.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerPosition();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.x = reader.float();
                        break;
                    }
                case 3: {
                        message.y = reader.float();
                        break;
                    }
                case 4: {
                        message.z = reader.float();
                        break;
                    }
                case 5: {
                        message.rotationX = reader.float();
                        break;
                    }
                case 6: {
                        message.rotationY = reader.float();
                        break;
                    }
                case 7: {
                        message.rotationZ = reader.float();
                        break;
                    }
                case 8: {
                        message.rotationW = reader.float();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerPosition message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerPosition} PlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerPosition.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerPosition message.
         * @function verify
         * @memberof game.PlayerPosition
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerPosition.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.x != null && message.hasOwnProperty("x"))
                if (typeof message.x !== "number")
                    return "x: number expected";
            if (message.y != null && message.hasOwnProperty("y"))
                if (typeof message.y !== "number")
                    return "y: number expected";
            if (message.z != null && message.hasOwnProperty("z"))
                if (typeof message.z !== "number")
                    return "z: number expected";
            if (message.rotationX != null && message.hasOwnProperty("rotationX"))
                if (typeof message.rotationX !== "number")
                    return "rotationX: number expected";
            if (message.rotationY != null && message.hasOwnProperty("rotationY"))
                if (typeof message.rotationY !== "number")
                    return "rotationY: number expected";
            if (message.rotationZ != null && message.hasOwnProperty("rotationZ"))
                if (typeof message.rotationZ !== "number")
                    return "rotationZ: number expected";
            if (message.rotationW != null && message.hasOwnProperty("rotationW"))
                if (typeof message.rotationW !== "number")
                    return "rotationW: number expected";
            return null;
        };

        /**
         * Creates a PlayerPosition message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerPosition
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerPosition} PlayerPosition
         */
        PlayerPosition.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerPosition)
                return object;
            var message = new $root.game.PlayerPosition();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.x != null)
                message.x = Number(object.x);
            if (object.y != null)
                message.y = Number(object.y);
            if (object.z != null)
                message.z = Number(object.z);
            if (object.rotationX != null)
                message.rotationX = Number(object.rotationX);
            if (object.rotationY != null)
                message.rotationY = Number(object.rotationY);
            if (object.rotationZ != null)
                message.rotationZ = Number(object.rotationZ);
            if (object.rotationW != null)
                message.rotationW = Number(object.rotationW);
            return message;
        };

        /**
         * Creates a plain object from a PlayerPosition message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerPosition
         * @static
         * @param {game.PlayerPosition} message PlayerPosition
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerPosition.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.btcAddress = "";
                object.x = 0;
                object.y = 0;
                object.z = 0;
                object.rotationX = 0;
                object.rotationY = 0;
                object.rotationZ = 0;
                object.rotationW = 0;
            }
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.x != null && message.hasOwnProperty("x"))
                object.x = options.json && !isFinite(message.x) ? String(message.x) : message.x;
            if (message.y != null && message.hasOwnProperty("y"))
                object.y = options.json && !isFinite(message.y) ? String(message.y) : message.y;
            if (message.z != null && message.hasOwnProperty("z"))
                object.z = options.json && !isFinite(message.z) ? String(message.z) : message.z;
            if (message.rotationX != null && message.hasOwnProperty("rotationX"))
                object.rotationX = options.json && !isFinite(message.rotationX) ? String(message.rotationX) : message.rotationX;
            if (message.rotationY != null && message.hasOwnProperty("rotationY"))
                object.rotationY = options.json && !isFinite(message.rotationY) ? String(message.rotationY) : message.rotationY;
            if (message.rotationZ != null && message.hasOwnProperty("rotationZ"))
                object.rotationZ = options.json && !isFinite(message.rotationZ) ? String(message.rotationZ) : message.rotationZ;
            if (message.rotationW != null && message.hasOwnProperty("rotationW"))
                object.rotationW = options.json && !isFinite(message.rotationW) ? String(message.rotationW) : message.rotationW;
            return object;
        };

        /**
         * Converts this PlayerPosition to JSON.
         * @function toJSON
         * @memberof game.PlayerPosition
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerPosition.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerPosition
         * @function getTypeUrl
         * @memberof game.PlayerPosition
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerPosition.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerPosition";
        };

        return PlayerPosition;
    })();

    game.PlayerAnimation = (function() {

        /**
         * Properties of a PlayerAnimation.
         * @memberof game
         * @interface IPlayerAnimation
         * @property {string|null} [btcAddress] PlayerAnimation btcAddress
         * @property {string|null} [animationName] PlayerAnimation animationName
         * @property {number|null} [speed] PlayerAnimation speed
         * @property {boolean|null} [loop] PlayerAnimation loop
         */

        /**
         * Constructs a new PlayerAnimation.
         * @memberof game
         * @classdesc Represents a PlayerAnimation.
         * @implements IPlayerAnimation
         * @constructor
         * @param {game.IPlayerAnimation=} [properties] Properties to set
         */
        function PlayerAnimation(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerAnimation btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerAnimation
         * @instance
         */
        PlayerAnimation.prototype.btcAddress = "";

        /**
         * PlayerAnimation animationName.
         * @member {string} animationName
         * @memberof game.PlayerAnimation
         * @instance
         */
        PlayerAnimation.prototype.animationName = "";

        /**
         * PlayerAnimation speed.
         * @member {number} speed
         * @memberof game.PlayerAnimation
         * @instance
         */
        PlayerAnimation.prototype.speed = 0;

        /**
         * PlayerAnimation loop.
         * @member {boolean} loop
         * @memberof game.PlayerAnimation
         * @instance
         */
        PlayerAnimation.prototype.loop = false;

        /**
         * Creates a new PlayerAnimation instance using the specified properties.
         * @function create
         * @memberof game.PlayerAnimation
         * @static
         * @param {game.IPlayerAnimation=} [properties] Properties to set
         * @returns {game.PlayerAnimation} PlayerAnimation instance
         */
        PlayerAnimation.create = function create(properties) {
            return new PlayerAnimation(properties);
        };

        /**
         * Encodes the specified PlayerAnimation message. Does not implicitly {@link game.PlayerAnimation.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerAnimation
         * @static
         * @param {game.IPlayerAnimation} message PlayerAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerAnimation.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            if (message.animationName != null && Object.hasOwnProperty.call(message, "animationName"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.animationName);
            if (message.speed != null && Object.hasOwnProperty.call(message, "speed"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.speed);
            if (message.loop != null && Object.hasOwnProperty.call(message, "loop"))
                writer.uint32(/* id 4, wireType 0 =*/32).bool(message.loop);
            return writer;
        };

        /**
         * Encodes the specified PlayerAnimation message, length delimited. Does not implicitly {@link game.PlayerAnimation.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerAnimation
         * @static
         * @param {game.IPlayerAnimation} message PlayerAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerAnimation.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerAnimation message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerAnimation} PlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerAnimation.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerAnimation();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.animationName = reader.string();
                        break;
                    }
                case 3: {
                        message.speed = reader.float();
                        break;
                    }
                case 4: {
                        message.loop = reader.bool();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerAnimation message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerAnimation} PlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerAnimation.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerAnimation message.
         * @function verify
         * @memberof game.PlayerAnimation
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerAnimation.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.animationName != null && message.hasOwnProperty("animationName"))
                if (!$util.isString(message.animationName))
                    return "animationName: string expected";
            if (message.speed != null && message.hasOwnProperty("speed"))
                if (typeof message.speed !== "number")
                    return "speed: number expected";
            if (message.loop != null && message.hasOwnProperty("loop"))
                if (typeof message.loop !== "boolean")
                    return "loop: boolean expected";
            return null;
        };

        /**
         * Creates a PlayerAnimation message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerAnimation
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerAnimation} PlayerAnimation
         */
        PlayerAnimation.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerAnimation)
                return object;
            var message = new $root.game.PlayerAnimation();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.animationName != null)
                message.animationName = String(object.animationName);
            if (object.speed != null)
                message.speed = Number(object.speed);
            if (object.loop != null)
                message.loop = Boolean(object.loop);
            return message;
        };

        /**
         * Creates a plain object from a PlayerAnimation message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerAnimation
         * @static
         * @param {game.PlayerAnimation} message PlayerAnimation
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerAnimation.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.btcAddress = "";
                object.animationName = "";
                object.speed = 0;
                object.loop = false;
            }
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.animationName != null && message.hasOwnProperty("animationName"))
                object.animationName = message.animationName;
            if (message.speed != null && message.hasOwnProperty("speed"))
                object.speed = options.json && !isFinite(message.speed) ? String(message.speed) : message.speed;
            if (message.loop != null && message.hasOwnProperty("loop"))
                object.loop = message.loop;
            return object;
        };

        /**
         * Converts this PlayerAnimation to JSON.
         * @function toJSON
         * @memberof game.PlayerAnimation
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerAnimation.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerAnimation
         * @function getTypeUrl
         * @memberof game.PlayerAnimation
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerAnimation.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerAnimation";
        };

        return PlayerAnimation;
    })();

    game.PlayerUpdate = (function() {

        /**
         * Properties of a PlayerUpdate.
         * @memberof game
         * @interface IPlayerUpdate
         * @property {string|null} [btcAddress] PlayerUpdate btcAddress
         * @property {string|null} [key] PlayerUpdate key
         * @property {string|null} [value] PlayerUpdate value
         */

        /**
         * Constructs a new PlayerUpdate.
         * @memberof game
         * @classdesc Represents a PlayerUpdate.
         * @implements IPlayerUpdate
         * @constructor
         * @param {game.IPlayerUpdate=} [properties] Properties to set
         */
        function PlayerUpdate(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerUpdate btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerUpdate
         * @instance
         */
        PlayerUpdate.prototype.btcAddress = "";

        /**
         * PlayerUpdate key.
         * @member {string} key
         * @memberof game.PlayerUpdate
         * @instance
         */
        PlayerUpdate.prototype.key = "";

        /**
         * PlayerUpdate value.
         * @member {string} value
         * @memberof game.PlayerUpdate
         * @instance
         */
        PlayerUpdate.prototype.value = "";

        /**
         * Creates a new PlayerUpdate instance using the specified properties.
         * @function create
         * @memberof game.PlayerUpdate
         * @static
         * @param {game.IPlayerUpdate=} [properties] Properties to set
         * @returns {game.PlayerUpdate} PlayerUpdate instance
         */
        PlayerUpdate.create = function create(properties) {
            return new PlayerUpdate(properties);
        };

        /**
         * Encodes the specified PlayerUpdate message. Does not implicitly {@link game.PlayerUpdate.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerUpdate
         * @static
         * @param {game.IPlayerUpdate} message PlayerUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerUpdate.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            if (message.key != null && Object.hasOwnProperty.call(message, "key"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.key);
            if (message.value != null && Object.hasOwnProperty.call(message, "value"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.value);
            return writer;
        };

        /**
         * Encodes the specified PlayerUpdate message, length delimited. Does not implicitly {@link game.PlayerUpdate.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerUpdate
         * @static
         * @param {game.IPlayerUpdate} message PlayerUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerUpdate.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerUpdate message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerUpdate} PlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerUpdate.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerUpdate();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.key = reader.string();
                        break;
                    }
                case 3: {
                        message.value = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerUpdate message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerUpdate} PlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerUpdate.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerUpdate message.
         * @function verify
         * @memberof game.PlayerUpdate
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerUpdate.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.key != null && message.hasOwnProperty("key"))
                if (!$util.isString(message.key))
                    return "key: string expected";
            if (message.value != null && message.hasOwnProperty("value"))
                if (!$util.isString(message.value))
                    return "value: string expected";
            return null;
        };

        /**
         * Creates a PlayerUpdate message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerUpdate
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerUpdate} PlayerUpdate
         */
        PlayerUpdate.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerUpdate)
                return object;
            var message = new $root.game.PlayerUpdate();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.key != null)
                message.key = String(object.key);
            if (object.value != null)
                message.value = String(object.value);
            return message;
        };

        /**
         * Creates a plain object from a PlayerUpdate message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerUpdate
         * @static
         * @param {game.PlayerUpdate} message PlayerUpdate
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerUpdate.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.btcAddress = "";
                object.key = "";
                object.value = "";
            }
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.key != null && message.hasOwnProperty("key"))
                object.key = message.key;
            if (message.value != null && message.hasOwnProperty("value"))
                object.value = message.value;
            return object;
        };

        /**
         * Converts this PlayerUpdate to JSON.
         * @function toJSON
         * @memberof game.PlayerUpdate
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerUpdate.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerUpdate
         * @function getTypeUrl
         * @memberof game.PlayerUpdate
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerUpdate.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerUpdate";
        };

        return PlayerUpdate;
    })();

    game.PlayerEnter = (function() {

        /**
         * Properties of a PlayerEnter.
         * @memberof game
         * @interface IPlayerEnter
         * @property {string|null} [btcAddress] PlayerEnter btcAddress
         * @property {string|null} [sessionId] PlayerEnter sessionId
         * @property {game.IAvatarData|null} [avatarData] PlayerEnter avatarData
         * @property {game.IPlayerPosition|null} [position] PlayerEnter position
         */

        /**
         * Constructs a new PlayerEnter.
         * @memberof game
         * @classdesc Represents a PlayerEnter.
         * @implements IPlayerEnter
         * @constructor
         * @param {game.IPlayerEnter=} [properties] Properties to set
         */
        function PlayerEnter(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerEnter btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerEnter
         * @instance
         */
        PlayerEnter.prototype.btcAddress = "";

        /**
         * PlayerEnter sessionId.
         * @member {string} sessionId
         * @memberof game.PlayerEnter
         * @instance
         */
        PlayerEnter.prototype.sessionId = "";

        /**
         * PlayerEnter avatarData.
         * @member {game.IAvatarData|null|undefined} avatarData
         * @memberof game.PlayerEnter
         * @instance
         */
        PlayerEnter.prototype.avatarData = null;

        /**
         * PlayerEnter position.
         * @member {game.IPlayerPosition|null|undefined} position
         * @memberof game.PlayerEnter
         * @instance
         */
        PlayerEnter.prototype.position = null;

        /**
         * Creates a new PlayerEnter instance using the specified properties.
         * @function create
         * @memberof game.PlayerEnter
         * @static
         * @param {game.IPlayerEnter=} [properties] Properties to set
         * @returns {game.PlayerEnter} PlayerEnter instance
         */
        PlayerEnter.create = function create(properties) {
            return new PlayerEnter(properties);
        };

        /**
         * Encodes the specified PlayerEnter message. Does not implicitly {@link game.PlayerEnter.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerEnter
         * @static
         * @param {game.IPlayerEnter} message PlayerEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerEnter.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            if (message.sessionId != null && Object.hasOwnProperty.call(message, "sessionId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.sessionId);
            if (message.avatarData != null && Object.hasOwnProperty.call(message, "avatarData"))
                $root.game.AvatarData.encode(message.avatarData, writer.uint32(/* id 3, wireType 2 =*/26).fork()).ldelim();
            if (message.position != null && Object.hasOwnProperty.call(message, "position"))
                $root.game.PlayerPosition.encode(message.position, writer.uint32(/* id 4, wireType 2 =*/34).fork()).ldelim();
            return writer;
        };

        /**
         * Encodes the specified PlayerEnter message, length delimited. Does not implicitly {@link game.PlayerEnter.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerEnter
         * @static
         * @param {game.IPlayerEnter} message PlayerEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerEnter.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerEnter message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerEnter} PlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerEnter.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerEnter();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.sessionId = reader.string();
                        break;
                    }
                case 3: {
                        message.avatarData = $root.game.AvatarData.decode(reader, reader.uint32());
                        break;
                    }
                case 4: {
                        message.position = $root.game.PlayerPosition.decode(reader, reader.uint32());
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerEnter message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerEnter} PlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerEnter.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerEnter message.
         * @function verify
         * @memberof game.PlayerEnter
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerEnter.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.sessionId != null && message.hasOwnProperty("sessionId"))
                if (!$util.isString(message.sessionId))
                    return "sessionId: string expected";
            if (message.avatarData != null && message.hasOwnProperty("avatarData")) {
                var error = $root.game.AvatarData.verify(message.avatarData);
                if (error)
                    return "avatarData." + error;
            }
            if (message.position != null && message.hasOwnProperty("position")) {
                var error = $root.game.PlayerPosition.verify(message.position);
                if (error)
                    return "position." + error;
            }
            return null;
        };

        /**
         * Creates a PlayerEnter message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerEnter
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerEnter} PlayerEnter
         */
        PlayerEnter.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerEnter)
                return object;
            var message = new $root.game.PlayerEnter();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.sessionId != null)
                message.sessionId = String(object.sessionId);
            if (object.avatarData != null) {
                if (typeof object.avatarData !== "object")
                    throw TypeError(".game.PlayerEnter.avatarData: object expected");
                message.avatarData = $root.game.AvatarData.fromObject(object.avatarData);
            }
            if (object.position != null) {
                if (typeof object.position !== "object")
                    throw TypeError(".game.PlayerEnter.position: object expected");
                message.position = $root.game.PlayerPosition.fromObject(object.position);
            }
            return message;
        };

        /**
         * Creates a plain object from a PlayerEnter message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerEnter
         * @static
         * @param {game.PlayerEnter} message PlayerEnter
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerEnter.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.btcAddress = "";
                object.sessionId = "";
                object.avatarData = null;
                object.position = null;
            }
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.sessionId != null && message.hasOwnProperty("sessionId"))
                object.sessionId = message.sessionId;
            if (message.avatarData != null && message.hasOwnProperty("avatarData"))
                object.avatarData = $root.game.AvatarData.toObject(message.avatarData, options);
            if (message.position != null && message.hasOwnProperty("position"))
                object.position = $root.game.PlayerPosition.toObject(message.position, options);
            return object;
        };

        /**
         * Converts this PlayerEnter to JSON.
         * @function toJSON
         * @memberof game.PlayerEnter
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerEnter.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerEnter
         * @function getTypeUrl
         * @memberof game.PlayerEnter
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerEnter.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerEnter";
        };

        return PlayerEnter;
    })();

    game.PlayerLeave = (function() {

        /**
         * Properties of a PlayerLeave.
         * @memberof game
         * @interface IPlayerLeave
         * @property {string|null} [btcAddress] PlayerLeave btcAddress
         */

        /**
         * Constructs a new PlayerLeave.
         * @memberof game
         * @classdesc Represents a PlayerLeave.
         * @implements IPlayerLeave
         * @constructor
         * @param {game.IPlayerLeave=} [properties] Properties to set
         */
        function PlayerLeave(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerLeave btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerLeave
         * @instance
         */
        PlayerLeave.prototype.btcAddress = "";

        /**
         * Creates a new PlayerLeave instance using the specified properties.
         * @function create
         * @memberof game.PlayerLeave
         * @static
         * @param {game.IPlayerLeave=} [properties] Properties to set
         * @returns {game.PlayerLeave} PlayerLeave instance
         */
        PlayerLeave.create = function create(properties) {
            return new PlayerLeave(properties);
        };

        /**
         * Encodes the specified PlayerLeave message. Does not implicitly {@link game.PlayerLeave.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerLeave
         * @static
         * @param {game.IPlayerLeave} message PlayerLeave message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerLeave.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            return writer;
        };

        /**
         * Encodes the specified PlayerLeave message, length delimited. Does not implicitly {@link game.PlayerLeave.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerLeave
         * @static
         * @param {game.IPlayerLeave} message PlayerLeave message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerLeave.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerLeave message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerLeave
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerLeave} PlayerLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerLeave.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerLeave();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerLeave message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerLeave
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerLeave} PlayerLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerLeave.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerLeave message.
         * @function verify
         * @memberof game.PlayerLeave
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerLeave.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            return null;
        };

        /**
         * Creates a PlayerLeave message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerLeave
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerLeave} PlayerLeave
         */
        PlayerLeave.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerLeave)
                return object;
            var message = new $root.game.PlayerLeave();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            return message;
        };

        /**
         * Creates a plain object from a PlayerLeave message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerLeave
         * @static
         * @param {game.PlayerLeave} message PlayerLeave
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerLeave.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.btcAddress = "";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            return object;
        };

        /**
         * Converts this PlayerLeave to JSON.
         * @function toJSON
         * @memberof game.PlayerLeave
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerLeave.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerLeave
         * @function getTypeUrl
         * @memberof game.PlayerLeave
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerLeave.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerLeave";
        };

        return PlayerLeave;
    })();

    game.AvatarData = (function() {

        /**
         * Properties of an AvatarData.
         * @memberof game
         * @interface IAvatarData
         * @property {string|null} [shirtId] AvatarData shirtId
         * @property {string|null} [shirtTextureId] AvatarData shirtTextureId
         * @property {string|null} [shirtColor] AvatarData shirtColor
         * @property {string|null} [pantsId] AvatarData pantsId
         * @property {string|null} [shoesId] AvatarData shoesId
         * @property {string|null} [hatId] AvatarData hatId
         */

        /**
         * Constructs a new AvatarData.
         * @memberof game
         * @classdesc Represents an AvatarData.
         * @implements IAvatarData
         * @constructor
         * @param {game.IAvatarData=} [properties] Properties to set
         */
        function AvatarData(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * AvatarData shirtId.
         * @member {string} shirtId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.shirtId = "";

        /**
         * AvatarData shirtTextureId.
         * @member {string} shirtTextureId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.shirtTextureId = "";

        /**
         * AvatarData shirtColor.
         * @member {string} shirtColor
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.shirtColor = "";

        /**
         * AvatarData pantsId.
         * @member {string} pantsId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.pantsId = "";

        /**
         * AvatarData shoesId.
         * @member {string} shoesId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.shoesId = "";

        /**
         * AvatarData hatId.
         * @member {string} hatId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.hatId = "";

        /**
         * Creates a new AvatarData instance using the specified properties.
         * @function create
         * @memberof game.AvatarData
         * @static
         * @param {game.IAvatarData=} [properties] Properties to set
         * @returns {game.AvatarData} AvatarData instance
         */
        AvatarData.create = function create(properties) {
            return new AvatarData(properties);
        };

        /**
         * Encodes the specified AvatarData message. Does not implicitly {@link game.AvatarData.verify|verify} messages.
         * @function encode
         * @memberof game.AvatarData
         * @static
         * @param {game.IAvatarData} message AvatarData message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        AvatarData.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.shirtId != null && Object.hasOwnProperty.call(message, "shirtId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.shirtId);
            if (message.shirtTextureId != null && Object.hasOwnProperty.call(message, "shirtTextureId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.shirtTextureId);
            if (message.shirtColor != null && Object.hasOwnProperty.call(message, "shirtColor"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.shirtColor);
            if (message.pantsId != null && Object.hasOwnProperty.call(message, "pantsId"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.pantsId);
            if (message.shoesId != null && Object.hasOwnProperty.call(message, "shoesId"))
                writer.uint32(/* id 5, wireType 2 =*/42).string(message.shoesId);
            if (message.hatId != null && Object.hasOwnProperty.call(message, "hatId"))
                writer.uint32(/* id 6, wireType 2 =*/50).string(message.hatId);
            return writer;
        };

        /**
         * Encodes the specified AvatarData message, length delimited. Does not implicitly {@link game.AvatarData.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.AvatarData
         * @static
         * @param {game.IAvatarData} message AvatarData message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        AvatarData.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an AvatarData message from the specified reader or buffer.
         * @function decode
         * @memberof game.AvatarData
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.AvatarData} AvatarData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        AvatarData.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.AvatarData();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.shirtId = reader.string();
                        break;
                    }
                case 2: {
                        message.shirtTextureId = reader.string();
                        break;
                    }
                case 3: {
                        message.shirtColor = reader.string();
                        break;
                    }
                case 4: {
                        message.pantsId = reader.string();
                        break;
                    }
                case 5: {
                        message.shoesId = reader.string();
                        break;
                    }
                case 6: {
                        message.hatId = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an AvatarData message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.AvatarData
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.AvatarData} AvatarData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        AvatarData.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an AvatarData message.
         * @function verify
         * @memberof game.AvatarData
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        AvatarData.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.shirtId != null && message.hasOwnProperty("shirtId"))
                if (!$util.isString(message.shirtId))
                    return "shirtId: string expected";
            if (message.shirtTextureId != null && message.hasOwnProperty("shirtTextureId"))
                if (!$util.isString(message.shirtTextureId))
                    return "shirtTextureId: string expected";
            if (message.shirtColor != null && message.hasOwnProperty("shirtColor"))
                if (!$util.isString(message.shirtColor))
                    return "shirtColor: string expected";
            if (message.pantsId != null && message.hasOwnProperty("pantsId"))
                if (!$util.isString(message.pantsId))
                    return "pantsId: string expected";
            if (message.shoesId != null && message.hasOwnProperty("shoesId"))
                if (!$util.isString(message.shoesId))
                    return "shoesId: string expected";
            if (message.hatId != null && message.hasOwnProperty("hatId"))
                if (!$util.isString(message.hatId))
                    return "hatId: string expected";
            return null;
        };

        /**
         * Creates an AvatarData message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.AvatarData
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.AvatarData} AvatarData
         */
        AvatarData.fromObject = function fromObject(object) {
            if (object instanceof $root.game.AvatarData)
                return object;
            var message = new $root.game.AvatarData();
            if (object.shirtId != null)
                message.shirtId = String(object.shirtId);
            if (object.shirtTextureId != null)
                message.shirtTextureId = String(object.shirtTextureId);
            if (object.shirtColor != null)
                message.shirtColor = String(object.shirtColor);
            if (object.pantsId != null)
                message.pantsId = String(object.pantsId);
            if (object.shoesId != null)
                message.shoesId = String(object.shoesId);
            if (object.hatId != null)
                message.hatId = String(object.hatId);
            return message;
        };

        /**
         * Creates a plain object from an AvatarData message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.AvatarData
         * @static
         * @param {game.AvatarData} message AvatarData
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        AvatarData.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.shirtId = "";
                object.shirtTextureId = "";
                object.shirtColor = "";
                object.pantsId = "";
                object.shoesId = "";
                object.hatId = "";
            }
            if (message.shirtId != null && message.hasOwnProperty("shirtId"))
                object.shirtId = message.shirtId;
            if (message.shirtTextureId != null && message.hasOwnProperty("shirtTextureId"))
                object.shirtTextureId = message.shirtTextureId;
            if (message.shirtColor != null && message.hasOwnProperty("shirtColor"))
                object.shirtColor = message.shirtColor;
            if (message.pantsId != null && message.hasOwnProperty("pantsId"))
                object.pantsId = message.pantsId;
            if (message.shoesId != null && message.hasOwnProperty("shoesId"))
                object.shoesId = message.shoesId;
            if (message.hatId != null && message.hasOwnProperty("hatId"))
                object.hatId = message.hatId;
            return object;
        };

        /**
         * Converts this AvatarData to JSON.
         * @function toJSON
         * @memberof game.AvatarData
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        AvatarData.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for AvatarData
         * @function getTypeUrl
         * @memberof game.AvatarData
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        AvatarData.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.AvatarData";
        };

        return AvatarData;
    })();

    game.PetPosition = (function() {

        /**
         * Properties of a PetPosition.
         * @memberof game
         * @interface IPetPosition
         * @property {string|null} [ownerBtcAddress] PetPosition ownerBtcAddress
         * @property {string|null} [petId] PetPosition petId
         * @property {number|null} [x] PetPosition x
         * @property {number|null} [y] PetPosition y
         * @property {number|null} [z] PetPosition z
         */

        /**
         * Constructs a new PetPosition.
         * @memberof game
         * @classdesc Represents a PetPosition.
         * @implements IPetPosition
         * @constructor
         * @param {game.IPetPosition=} [properties] Properties to set
         */
        function PetPosition(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PetPosition ownerBtcAddress.
         * @member {string} ownerBtcAddress
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.ownerBtcAddress = "";

        /**
         * PetPosition petId.
         * @member {string} petId
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.petId = "";

        /**
         * PetPosition x.
         * @member {number} x
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.x = 0;

        /**
         * PetPosition y.
         * @member {number} y
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.y = 0;

        /**
         * PetPosition z.
         * @member {number} z
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.z = 0;

        /**
         * Creates a new PetPosition instance using the specified properties.
         * @function create
         * @memberof game.PetPosition
         * @static
         * @param {game.IPetPosition=} [properties] Properties to set
         * @returns {game.PetPosition} PetPosition instance
         */
        PetPosition.create = function create(properties) {
            return new PetPosition(properties);
        };

        /**
         * Encodes the specified PetPosition message. Does not implicitly {@link game.PetPosition.verify|verify} messages.
         * @function encode
         * @memberof game.PetPosition
         * @static
         * @param {game.IPetPosition} message PetPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PetPosition.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.ownerBtcAddress != null && Object.hasOwnProperty.call(message, "ownerBtcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.ownerBtcAddress);
            if (message.petId != null && Object.hasOwnProperty.call(message, "petId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.petId);
            if (message.x != null && Object.hasOwnProperty.call(message, "x"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.x);
            if (message.y != null && Object.hasOwnProperty.call(message, "y"))
                writer.uint32(/* id 4, wireType 5 =*/37).float(message.y);
            if (message.z != null && Object.hasOwnProperty.call(message, "z"))
                writer.uint32(/* id 5, wireType 5 =*/45).float(message.z);
            return writer;
        };

        /**
         * Encodes the specified PetPosition message, length delimited. Does not implicitly {@link game.PetPosition.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PetPosition
         * @static
         * @param {game.IPetPosition} message PetPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PetPosition.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PetPosition message from the specified reader or buffer.
         * @function decode
         * @memberof game.PetPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PetPosition} PetPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PetPosition.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PetPosition();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.ownerBtcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.petId = reader.string();
                        break;
                    }
                case 3: {
                        message.x = reader.float();
                        break;
                    }
                case 4: {
                        message.y = reader.float();
                        break;
                    }
                case 5: {
                        message.z = reader.float();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PetPosition message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PetPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PetPosition} PetPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PetPosition.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PetPosition message.
         * @function verify
         * @memberof game.PetPosition
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PetPosition.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.ownerBtcAddress != null && message.hasOwnProperty("ownerBtcAddress"))
                if (!$util.isString(message.ownerBtcAddress))
                    return "ownerBtcAddress: string expected";
            if (message.petId != null && message.hasOwnProperty("petId"))
                if (!$util.isString(message.petId))
                    return "petId: string expected";
            if (message.x != null && message.hasOwnProperty("x"))
                if (typeof message.x !== "number")
                    return "x: number expected";
            if (message.y != null && message.hasOwnProperty("y"))
                if (typeof message.y !== "number")
                    return "y: number expected";
            if (message.z != null && message.hasOwnProperty("z"))
                if (typeof message.z !== "number")
                    return "z: number expected";
            return null;
        };

        /**
         * Creates a PetPosition message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PetPosition
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PetPosition} PetPosition
         */
        PetPosition.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PetPosition)
                return object;
            var message = new $root.game.PetPosition();
            if (object.ownerBtcAddress != null)
                message.ownerBtcAddress = String(object.ownerBtcAddress);
            if (object.petId != null)
                message.petId = String(object.petId);
            if (object.x != null)
                message.x = Number(object.x);
            if (object.y != null)
                message.y = Number(object.y);
            if (object.z != null)
                message.z = Number(object.z);
            return message;
        };

        /**
         * Creates a plain object from a PetPosition message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PetPosition
         * @static
         * @param {game.PetPosition} message PetPosition
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PetPosition.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.ownerBtcAddress = "";
                object.petId = "";
                object.x = 0;
                object.y = 0;
                object.z = 0;
            }
            if (message.ownerBtcAddress != null && message.hasOwnProperty("ownerBtcAddress"))
                object.ownerBtcAddress = message.ownerBtcAddress;
            if (message.petId != null && message.hasOwnProperty("petId"))
                object.petId = message.petId;
            if (message.x != null && message.hasOwnProperty("x"))
                object.x = options.json && !isFinite(message.x) ? String(message.x) : message.x;
            if (message.y != null && message.hasOwnProperty("y"))
                object.y = options.json && !isFinite(message.y) ? String(message.y) : message.y;
            if (message.z != null && message.hasOwnProperty("z"))
                object.z = options.json && !isFinite(message.z) ? String(message.z) : message.z;
            return object;
        };

        /**
         * Converts this PetPosition to JSON.
         * @function toJSON
         * @memberof game.PetPosition
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PetPosition.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PetPosition
         * @function getTypeUrl
         * @memberof game.PetPosition
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PetPosition.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PetPosition";
        };

        return PetPosition;
    })();

    game.PetAnimation = (function() {

        /**
         * Properties of a PetAnimation.
         * @memberof game
         * @interface IPetAnimation
         * @property {string|null} [ownerBtcAddress] PetAnimation ownerBtcAddress
         * @property {string|null} [petId] PetAnimation petId
         * @property {string|null} [animationName] PetAnimation animationName
         */

        /**
         * Constructs a new PetAnimation.
         * @memberof game
         * @classdesc Represents a PetAnimation.
         * @implements IPetAnimation
         * @constructor
         * @param {game.IPetAnimation=} [properties] Properties to set
         */
        function PetAnimation(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PetAnimation ownerBtcAddress.
         * @member {string} ownerBtcAddress
         * @memberof game.PetAnimation
         * @instance
         */
        PetAnimation.prototype.ownerBtcAddress = "";

        /**
         * PetAnimation petId.
         * @member {string} petId
         * @memberof game.PetAnimation
         * @instance
         */
        PetAnimation.prototype.petId = "";

        /**
         * PetAnimation animationName.
         * @member {string} animationName
         * @memberof game.PetAnimation
         * @instance
         */
        PetAnimation.prototype.animationName = "";

        /**
         * Creates a new PetAnimation instance using the specified properties.
         * @function create
         * @memberof game.PetAnimation
         * @static
         * @param {game.IPetAnimation=} [properties] Properties to set
         * @returns {game.PetAnimation} PetAnimation instance
         */
        PetAnimation.create = function create(properties) {
            return new PetAnimation(properties);
        };

        /**
         * Encodes the specified PetAnimation message. Does not implicitly {@link game.PetAnimation.verify|verify} messages.
         * @function encode
         * @memberof game.PetAnimation
         * @static
         * @param {game.IPetAnimation} message PetAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PetAnimation.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.ownerBtcAddress != null && Object.hasOwnProperty.call(message, "ownerBtcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.ownerBtcAddress);
            if (message.petId != null && Object.hasOwnProperty.call(message, "petId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.petId);
            if (message.animationName != null && Object.hasOwnProperty.call(message, "animationName"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.animationName);
            return writer;
        };

        /**
         * Encodes the specified PetAnimation message, length delimited. Does not implicitly {@link game.PetAnimation.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PetAnimation
         * @static
         * @param {game.IPetAnimation} message PetAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PetAnimation.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PetAnimation message from the specified reader or buffer.
         * @function decode
         * @memberof game.PetAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PetAnimation} PetAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PetAnimation.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PetAnimation();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.ownerBtcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.petId = reader.string();
                        break;
                    }
                case 3: {
                        message.animationName = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PetAnimation message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PetAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PetAnimation} PetAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PetAnimation.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PetAnimation message.
         * @function verify
         * @memberof game.PetAnimation
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PetAnimation.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.ownerBtcAddress != null && message.hasOwnProperty("ownerBtcAddress"))
                if (!$util.isString(message.ownerBtcAddress))
                    return "ownerBtcAddress: string expected";
            if (message.petId != null && message.hasOwnProperty("petId"))
                if (!$util.isString(message.petId))
                    return "petId: string expected";
            if (message.animationName != null && message.hasOwnProperty("animationName"))
                if (!$util.isString(message.animationName))
                    return "animationName: string expected";
            return null;
        };

        /**
         * Creates a PetAnimation message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PetAnimation
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PetAnimation} PetAnimation
         */
        PetAnimation.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PetAnimation)
                return object;
            var message = new $root.game.PetAnimation();
            if (object.ownerBtcAddress != null)
                message.ownerBtcAddress = String(object.ownerBtcAddress);
            if (object.petId != null)
                message.petId = String(object.petId);
            if (object.animationName != null)
                message.animationName = String(object.animationName);
            return message;
        };

        /**
         * Creates a plain object from a PetAnimation message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PetAnimation
         * @static
         * @param {game.PetAnimation} message PetAnimation
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PetAnimation.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.ownerBtcAddress = "";
                object.petId = "";
                object.animationName = "";
            }
            if (message.ownerBtcAddress != null && message.hasOwnProperty("ownerBtcAddress"))
                object.ownerBtcAddress = message.ownerBtcAddress;
            if (message.petId != null && message.hasOwnProperty("petId"))
                object.petId = message.petId;
            if (message.animationName != null && message.hasOwnProperty("animationName"))
                object.animationName = message.animationName;
            return object;
        };

        /**
         * Converts this PetAnimation to JSON.
         * @function toJSON
         * @memberof game.PetAnimation
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PetAnimation.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PetAnimation
         * @function getTypeUrl
         * @memberof game.PetAnimation
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PetAnimation.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PetAnimation";
        };

        return PetAnimation;
    })();

    game.ChatEnter = (function() {

        /**
         * Properties of a ChatEnter.
         * @memberof game
         * @interface IChatEnter
         * @property {number|null} [chatId] ChatEnter chatId
         * @property {string|null} [btcAddress] ChatEnter btcAddress
         */

        /**
         * Constructs a new ChatEnter.
         * @memberof game
         * @classdesc Represents a ChatEnter.
         * @implements IChatEnter
         * @constructor
         * @param {game.IChatEnter=} [properties] Properties to set
         */
        function ChatEnter(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ChatEnter chatId.
         * @member {number} chatId
         * @memberof game.ChatEnter
         * @instance
         */
        ChatEnter.prototype.chatId = 0;

        /**
         * ChatEnter btcAddress.
         * @member {string} btcAddress
         * @memberof game.ChatEnter
         * @instance
         */
        ChatEnter.prototype.btcAddress = "";

        /**
         * Creates a new ChatEnter instance using the specified properties.
         * @function create
         * @memberof game.ChatEnter
         * @static
         * @param {game.IChatEnter=} [properties] Properties to set
         * @returns {game.ChatEnter} ChatEnter instance
         */
        ChatEnter.create = function create(properties) {
            return new ChatEnter(properties);
        };

        /**
         * Encodes the specified ChatEnter message. Does not implicitly {@link game.ChatEnter.verify|verify} messages.
         * @function encode
         * @memberof game.ChatEnter
         * @static
         * @param {game.IChatEnter} message ChatEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatEnter.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.chatId != null && Object.hasOwnProperty.call(message, "chatId"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.chatId);
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.btcAddress);
            return writer;
        };

        /**
         * Encodes the specified ChatEnter message, length delimited. Does not implicitly {@link game.ChatEnter.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ChatEnter
         * @static
         * @param {game.IChatEnter} message ChatEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatEnter.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ChatEnter message from the specified reader or buffer.
         * @function decode
         * @memberof game.ChatEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ChatEnter} ChatEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatEnter.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ChatEnter();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.chatId = reader.int32();
                        break;
                    }
                case 2: {
                        message.btcAddress = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ChatEnter message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ChatEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ChatEnter} ChatEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatEnter.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ChatEnter message.
         * @function verify
         * @memberof game.ChatEnter
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ChatEnter.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                if (!$util.isInteger(message.chatId))
                    return "chatId: integer expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            return null;
        };

        /**
         * Creates a ChatEnter message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ChatEnter
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ChatEnter} ChatEnter
         */
        ChatEnter.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ChatEnter)
                return object;
            var message = new $root.game.ChatEnter();
            if (object.chatId != null)
                message.chatId = object.chatId | 0;
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            return message;
        };

        /**
         * Creates a plain object from a ChatEnter message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ChatEnter
         * @static
         * @param {game.ChatEnter} message ChatEnter
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ChatEnter.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.chatId = 0;
                object.btcAddress = "";
            }
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                object.chatId = message.chatId;
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            return object;
        };

        /**
         * Converts this ChatEnter to JSON.
         * @function toJSON
         * @memberof game.ChatEnter
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ChatEnter.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ChatEnter
         * @function getTypeUrl
         * @memberof game.ChatEnter
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ChatEnter.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ChatEnter";
        };

        return ChatEnter;
    })();

    game.ChatLeave = (function() {

        /**
         * Properties of a ChatLeave.
         * @memberof game
         * @interface IChatLeave
         * @property {number|null} [chatId] ChatLeave chatId
         * @property {string|null} [btcAddress] ChatLeave btcAddress
         */

        /**
         * Constructs a new ChatLeave.
         * @memberof game
         * @classdesc Represents a ChatLeave.
         * @implements IChatLeave
         * @constructor
         * @param {game.IChatLeave=} [properties] Properties to set
         */
        function ChatLeave(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ChatLeave chatId.
         * @member {number} chatId
         * @memberof game.ChatLeave
         * @instance
         */
        ChatLeave.prototype.chatId = 0;

        /**
         * ChatLeave btcAddress.
         * @member {string} btcAddress
         * @memberof game.ChatLeave
         * @instance
         */
        ChatLeave.prototype.btcAddress = "";

        /**
         * Creates a new ChatLeave instance using the specified properties.
         * @function create
         * @memberof game.ChatLeave
         * @static
         * @param {game.IChatLeave=} [properties] Properties to set
         * @returns {game.ChatLeave} ChatLeave instance
         */
        ChatLeave.create = function create(properties) {
            return new ChatLeave(properties);
        };

        /**
         * Encodes the specified ChatLeave message. Does not implicitly {@link game.ChatLeave.verify|verify} messages.
         * @function encode
         * @memberof game.ChatLeave
         * @static
         * @param {game.IChatLeave} message ChatLeave message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatLeave.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.chatId != null && Object.hasOwnProperty.call(message, "chatId"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.chatId);
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.btcAddress);
            return writer;
        };

        /**
         * Encodes the specified ChatLeave message, length delimited. Does not implicitly {@link game.ChatLeave.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ChatLeave
         * @static
         * @param {game.IChatLeave} message ChatLeave message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatLeave.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ChatLeave message from the specified reader or buffer.
         * @function decode
         * @memberof game.ChatLeave
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ChatLeave} ChatLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatLeave.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ChatLeave();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.chatId = reader.int32();
                        break;
                    }
                case 2: {
                        message.btcAddress = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ChatLeave message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ChatLeave
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ChatLeave} ChatLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatLeave.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ChatLeave message.
         * @function verify
         * @memberof game.ChatLeave
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ChatLeave.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                if (!$util.isInteger(message.chatId))
                    return "chatId: integer expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            return null;
        };

        /**
         * Creates a ChatLeave message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ChatLeave
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ChatLeave} ChatLeave
         */
        ChatLeave.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ChatLeave)
                return object;
            var message = new $root.game.ChatLeave();
            if (object.chatId != null)
                message.chatId = object.chatId | 0;
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            return message;
        };

        /**
         * Creates a plain object from a ChatLeave message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ChatLeave
         * @static
         * @param {game.ChatLeave} message ChatLeave
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ChatLeave.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.chatId = 0;
                object.btcAddress = "";
            }
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                object.chatId = message.chatId;
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            return object;
        };

        /**
         * Converts this ChatLeave to JSON.
         * @function toJSON
         * @memberof game.ChatLeave
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ChatLeave.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ChatLeave
         * @function getTypeUrl
         * @memberof game.ChatLeave
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ChatLeave.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ChatLeave";
        };

        return ChatLeave;
    })();

    game.ChatMessage = (function() {

        /**
         * Properties of a ChatMessage.
         * @memberof game
         * @interface IChatMessage
         * @property {string|null} [uuid] ChatMessage uuid
         * @property {string|null} [playerId] ChatMessage playerId
         * @property {string|null} [content] ChatMessage content
         * @property {string|null} [replyTo] ChatMessage replyTo
         * @property {number|Long|null} [timestamp] ChatMessage timestamp
         * @property {number|null} [tabType] ChatMessage tabType
         */

        /**
         * Constructs a new ChatMessage.
         * @memberof game
         * @classdesc Represents a ChatMessage.
         * @implements IChatMessage
         * @constructor
         * @param {game.IChatMessage=} [properties] Properties to set
         */
        function ChatMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ChatMessage uuid.
         * @member {string} uuid
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.uuid = "";

        /**
         * ChatMessage playerId.
         * @member {string} playerId
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.playerId = "";

        /**
         * ChatMessage content.
         * @member {string} content
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.content = "";

        /**
         * ChatMessage replyTo.
         * @member {string} replyTo
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.replyTo = "";

        /**
         * ChatMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * ChatMessage tabType.
         * @member {number} tabType
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.tabType = 0;

        /**
         * Creates a new ChatMessage instance using the specified properties.
         * @function create
         * @memberof game.ChatMessage
         * @static
         * @param {game.IChatMessage=} [properties] Properties to set
         * @returns {game.ChatMessage} ChatMessage instance
         */
        ChatMessage.create = function create(properties) {
            return new ChatMessage(properties);
        };

        /**
         * Encodes the specified ChatMessage message. Does not implicitly {@link game.ChatMessage.verify|verify} messages.
         * @function encode
         * @memberof game.ChatMessage
         * @static
         * @param {game.IChatMessage} message ChatMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.uuid != null && Object.hasOwnProperty.call(message, "uuid"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.uuid);
            if (message.playerId != null && Object.hasOwnProperty.call(message, "playerId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.playerId);
            if (message.content != null && Object.hasOwnProperty.call(message, "content"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.content);
            if (message.replyTo != null && Object.hasOwnProperty.call(message, "replyTo"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.replyTo);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 5, wireType 0 =*/40).int64(message.timestamp);
            if (message.tabType != null && Object.hasOwnProperty.call(message, "tabType"))
                writer.uint32(/* id 6, wireType 0 =*/48).int32(message.tabType);
            return writer;
        };

        /**
         * Encodes the specified ChatMessage message, length delimited. Does not implicitly {@link game.ChatMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ChatMessage
         * @static
         * @param {game.IChatMessage} message ChatMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ChatMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.ChatMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ChatMessage} ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ChatMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.uuid = reader.string();
                        break;
                    }
                case 2: {
                        message.playerId = reader.string();
                        break;
                    }
                case 3: {
                        message.content = reader.string();
                        break;
                    }
                case 4: {
                        message.replyTo = reader.string();
                        break;
                    }
                case 5: {
                        message.timestamp = reader.int64();
                        break;
                    }
                case 6: {
                        message.tabType = reader.int32();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ChatMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ChatMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ChatMessage} ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ChatMessage message.
         * @function verify
         * @memberof game.ChatMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ChatMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.uuid != null && message.hasOwnProperty("uuid"))
                if (!$util.isString(message.uuid))
                    return "uuid: string expected";
            if (message.playerId != null && message.hasOwnProperty("playerId"))
                if (!$util.isString(message.playerId))
                    return "playerId: string expected";
            if (message.content != null && message.hasOwnProperty("content"))
                if (!$util.isString(message.content))
                    return "content: string expected";
            if (message.replyTo != null && message.hasOwnProperty("replyTo"))
                if (!$util.isString(message.replyTo))
                    return "replyTo: string expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            if (message.tabType != null && message.hasOwnProperty("tabType"))
                if (!$util.isInteger(message.tabType))
                    return "tabType: integer expected";
            return null;
        };

        /**
         * Creates a ChatMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ChatMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ChatMessage} ChatMessage
         */
        ChatMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ChatMessage)
                return object;
            var message = new $root.game.ChatMessage();
            if (object.uuid != null)
                message.uuid = String(object.uuid);
            if (object.playerId != null)
                message.playerId = String(object.playerId);
            if (object.content != null)
                message.content = String(object.content);
            if (object.replyTo != null)
                message.replyTo = String(object.replyTo);
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            if (object.tabType != null)
                message.tabType = object.tabType | 0;
            return message;
        };

        /**
         * Creates a plain object from a ChatMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ChatMessage
         * @static
         * @param {game.ChatMessage} message ChatMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ChatMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.uuid = "";
                object.playerId = "";
                object.content = "";
                object.replyTo = "";
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
                object.tabType = 0;
            }
            if (message.uuid != null && message.hasOwnProperty("uuid"))
                object.uuid = message.uuid;
            if (message.playerId != null && message.hasOwnProperty("playerId"))
                object.playerId = message.playerId;
            if (message.content != null && message.hasOwnProperty("content"))
                object.content = message.content;
            if (message.replyTo != null && message.hasOwnProperty("replyTo"))
                object.replyTo = message.replyTo;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            if (message.tabType != null && message.hasOwnProperty("tabType"))
                object.tabType = message.tabType;
            return object;
        };

        /**
         * Converts this ChatMessage to JSON.
         * @function toJSON
         * @memberof game.ChatMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ChatMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ChatMessage
         * @function getTypeUrl
         * @memberof game.ChatMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ChatMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ChatMessage";
        };

        return ChatMessage;
    })();

    game.RedPacketUpdate = (function() {

        /**
         * Properties of a RedPacketUpdate.
         * @memberof game
         * @interface IRedPacketUpdate
         * @property {string|null} [packetId] RedPacketUpdate packetId
         * @property {string|null} [creatorAddress] RedPacketUpdate creatorAddress
         * @property {number|null} [amount] RedPacketUpdate amount
         * @property {number|null} [count] RedPacketUpdate count
         * @property {number|null} [remaining] RedPacketUpdate remaining
         * @property {string|null} [status] RedPacketUpdate status
         * @property {number|Long|null} [createdAt] RedPacketUpdate createdAt
         * @property {number|Long|null} [expiresAt] RedPacketUpdate expiresAt
         */

        /**
         * Constructs a new RedPacketUpdate.
         * @memberof game
         * @classdesc Represents a RedPacketUpdate.
         * @implements IRedPacketUpdate
         * @constructor
         * @param {game.IRedPacketUpdate=} [properties] Properties to set
         */
        function RedPacketUpdate(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * RedPacketUpdate packetId.
         * @member {string} packetId
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.packetId = "";

        /**
         * RedPacketUpdate creatorAddress.
         * @member {string} creatorAddress
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.creatorAddress = "";

        /**
         * RedPacketUpdate amount.
         * @member {number} amount
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.amount = 0;

        /**
         * RedPacketUpdate count.
         * @member {number} count
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.count = 0;

        /**
         * RedPacketUpdate remaining.
         * @member {number} remaining
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.remaining = 0;

        /**
         * RedPacketUpdate status.
         * @member {string} status
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.status = "";

        /**
         * RedPacketUpdate createdAt.
         * @member {number|Long} createdAt
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.createdAt = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * RedPacketUpdate expiresAt.
         * @member {number|Long} expiresAt
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.expiresAt = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new RedPacketUpdate instance using the specified properties.
         * @function create
         * @memberof game.RedPacketUpdate
         * @static
         * @param {game.IRedPacketUpdate=} [properties] Properties to set
         * @returns {game.RedPacketUpdate} RedPacketUpdate instance
         */
        RedPacketUpdate.create = function create(properties) {
            return new RedPacketUpdate(properties);
        };

        /**
         * Encodes the specified RedPacketUpdate message. Does not implicitly {@link game.RedPacketUpdate.verify|verify} messages.
         * @function encode
         * @memberof game.RedPacketUpdate
         * @static
         * @param {game.IRedPacketUpdate} message RedPacketUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketUpdate.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.packetId != null && Object.hasOwnProperty.call(message, "packetId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.packetId);
            if (message.creatorAddress != null && Object.hasOwnProperty.call(message, "creatorAddress"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.creatorAddress);
            if (message.amount != null && Object.hasOwnProperty.call(message, "amount"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.amount);
            if (message.count != null && Object.hasOwnProperty.call(message, "count"))
                writer.uint32(/* id 4, wireType 0 =*/32).int32(message.count);
            if (message.remaining != null && Object.hasOwnProperty.call(message, "remaining"))
                writer.uint32(/* id 5, wireType 0 =*/40).int32(message.remaining);
            if (message.status != null && Object.hasOwnProperty.call(message, "status"))
                writer.uint32(/* id 6, wireType 2 =*/50).string(message.status);
            if (message.createdAt != null && Object.hasOwnProperty.call(message, "createdAt"))
                writer.uint32(/* id 7, wireType 0 =*/56).int64(message.createdAt);
            if (message.expiresAt != null && Object.hasOwnProperty.call(message, "expiresAt"))
                writer.uint32(/* id 8, wireType 0 =*/64).int64(message.expiresAt);
            return writer;
        };

        /**
         * Encodes the specified RedPacketUpdate message, length delimited. Does not implicitly {@link game.RedPacketUpdate.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.RedPacketUpdate
         * @static
         * @param {game.IRedPacketUpdate} message RedPacketUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketUpdate.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a RedPacketUpdate message from the specified reader or buffer.
         * @function decode
         * @memberof game.RedPacketUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.RedPacketUpdate} RedPacketUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketUpdate.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.RedPacketUpdate();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.packetId = reader.string();
                        break;
                    }
                case 2: {
                        message.creatorAddress = reader.string();
                        break;
                    }
                case 3: {
                        message.amount = reader.float();
                        break;
                    }
                case 4: {
                        message.count = reader.int32();
                        break;
                    }
                case 5: {
                        message.remaining = reader.int32();
                        break;
                    }
                case 6: {
                        message.status = reader.string();
                        break;
                    }
                case 7: {
                        message.createdAt = reader.int64();
                        break;
                    }
                case 8: {
                        message.expiresAt = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a RedPacketUpdate message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.RedPacketUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.RedPacketUpdate} RedPacketUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketUpdate.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a RedPacketUpdate message.
         * @function verify
         * @memberof game.RedPacketUpdate
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        RedPacketUpdate.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.packetId != null && message.hasOwnProperty("packetId"))
                if (!$util.isString(message.packetId))
                    return "packetId: string expected";
            if (message.creatorAddress != null && message.hasOwnProperty("creatorAddress"))
                if (!$util.isString(message.creatorAddress))
                    return "creatorAddress: string expected";
            if (message.amount != null && message.hasOwnProperty("amount"))
                if (typeof message.amount !== "number")
                    return "amount: number expected";
            if (message.count != null && message.hasOwnProperty("count"))
                if (!$util.isInteger(message.count))
                    return "count: integer expected";
            if (message.remaining != null && message.hasOwnProperty("remaining"))
                if (!$util.isInteger(message.remaining))
                    return "remaining: integer expected";
            if (message.status != null && message.hasOwnProperty("status"))
                if (!$util.isString(message.status))
                    return "status: string expected";
            if (message.createdAt != null && message.hasOwnProperty("createdAt"))
                if (!$util.isInteger(message.createdAt) && !(message.createdAt && $util.isInteger(message.createdAt.low) && $util.isInteger(message.createdAt.high)))
                    return "createdAt: integer|Long expected";
            if (message.expiresAt != null && message.hasOwnProperty("expiresAt"))
                if (!$util.isInteger(message.expiresAt) && !(message.expiresAt && $util.isInteger(message.expiresAt.low) && $util.isInteger(message.expiresAt.high)))
                    return "expiresAt: integer|Long expected";
            return null;
        };

        /**
         * Creates a RedPacketUpdate message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.RedPacketUpdate
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.RedPacketUpdate} RedPacketUpdate
         */
        RedPacketUpdate.fromObject = function fromObject(object) {
            if (object instanceof $root.game.RedPacketUpdate)
                return object;
            var message = new $root.game.RedPacketUpdate();
            if (object.packetId != null)
                message.packetId = String(object.packetId);
            if (object.creatorAddress != null)
                message.creatorAddress = String(object.creatorAddress);
            if (object.amount != null)
                message.amount = Number(object.amount);
            if (object.count != null)
                message.count = object.count | 0;
            if (object.remaining != null)
                message.remaining = object.remaining | 0;
            if (object.status != null)
                message.status = String(object.status);
            if (object.createdAt != null)
                if ($util.Long)
                    (message.createdAt = $util.Long.fromValue(object.createdAt)).unsigned = false;
                else if (typeof object.createdAt === "string")
                    message.createdAt = parseInt(object.createdAt, 10);
                else if (typeof object.createdAt === "number")
                    message.createdAt = object.createdAt;
                else if (typeof object.createdAt === "object")
                    message.createdAt = new $util.LongBits(object.createdAt.low >>> 0, object.createdAt.high >>> 0).toNumber();
            if (object.expiresAt != null)
                if ($util.Long)
                    (message.expiresAt = $util.Long.fromValue(object.expiresAt)).unsigned = false;
                else if (typeof object.expiresAt === "string")
                    message.expiresAt = parseInt(object.expiresAt, 10);
                else if (typeof object.expiresAt === "number")
                    message.expiresAt = object.expiresAt;
                else if (typeof object.expiresAt === "object")
                    message.expiresAt = new $util.LongBits(object.expiresAt.low >>> 0, object.expiresAt.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a RedPacketUpdate message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.RedPacketUpdate
         * @static
         * @param {game.RedPacketUpdate} message RedPacketUpdate
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        RedPacketUpdate.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.packetId = "";
                object.creatorAddress = "";
                object.amount = 0;
                object.count = 0;
                object.remaining = 0;
                object.status = "";
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.createdAt = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.createdAt = options.longs === String ? "0" : 0;
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.expiresAt = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.expiresAt = options.longs === String ? "0" : 0;
            }
            if (message.packetId != null && message.hasOwnProperty("packetId"))
                object.packetId = message.packetId;
            if (message.creatorAddress != null && message.hasOwnProperty("creatorAddress"))
                object.creatorAddress = message.creatorAddress;
            if (message.amount != null && message.hasOwnProperty("amount"))
                object.amount = options.json && !isFinite(message.amount) ? String(message.amount) : message.amount;
            if (message.count != null && message.hasOwnProperty("count"))
                object.count = message.count;
            if (message.remaining != null && message.hasOwnProperty("remaining"))
                object.remaining = message.remaining;
            if (message.status != null && message.hasOwnProperty("status"))
                object.status = message.status;
            if (message.createdAt != null && message.hasOwnProperty("createdAt"))
                if (typeof message.createdAt === "number")
                    object.createdAt = options.longs === String ? String(message.createdAt) : message.createdAt;
                else
                    object.createdAt = options.longs === String ? $util.Long.prototype.toString.call(message.createdAt) : options.longs === Number ? new $util.LongBits(message.createdAt.low >>> 0, message.createdAt.high >>> 0).toNumber() : message.createdAt;
            if (message.expiresAt != null && message.hasOwnProperty("expiresAt"))
                if (typeof message.expiresAt === "number")
                    object.expiresAt = options.longs === String ? String(message.expiresAt) : message.expiresAt;
                else
                    object.expiresAt = options.longs === String ? $util.Long.prototype.toString.call(message.expiresAt) : options.longs === Number ? new $util.LongBits(message.expiresAt.low >>> 0, message.expiresAt.high >>> 0).toNumber() : message.expiresAt;
            return object;
        };

        /**
         * Converts this RedPacketUpdate to JSON.
         * @function toJSON
         * @memberof game.RedPacketUpdate
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        RedPacketUpdate.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for RedPacketUpdate
         * @function getTypeUrl
         * @memberof game.RedPacketUpdate
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        RedPacketUpdate.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.RedPacketUpdate";
        };

        return RedPacketUpdate;
    })();

    game.RedPacketReward = (function() {

        /**
         * Properties of a RedPacketReward.
         * @memberof game
         * @interface IRedPacketReward
         * @property {string|null} [packetId] RedPacketReward packetId
         * @property {string|null} [receiverAddress] RedPacketReward receiverAddress
         * @property {number|null} [amount] RedPacketReward amount
         * @property {number|Long|null} [receivedAt] RedPacketReward receivedAt
         */

        /**
         * Constructs a new RedPacketReward.
         * @memberof game
         * @classdesc Represents a RedPacketReward.
         * @implements IRedPacketReward
         * @constructor
         * @param {game.IRedPacketReward=} [properties] Properties to set
         */
        function RedPacketReward(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * RedPacketReward packetId.
         * @member {string} packetId
         * @memberof game.RedPacketReward
         * @instance
         */
        RedPacketReward.prototype.packetId = "";

        /**
         * RedPacketReward receiverAddress.
         * @member {string} receiverAddress
         * @memberof game.RedPacketReward
         * @instance
         */
        RedPacketReward.prototype.receiverAddress = "";

        /**
         * RedPacketReward amount.
         * @member {number} amount
         * @memberof game.RedPacketReward
         * @instance
         */
        RedPacketReward.prototype.amount = 0;

        /**
         * RedPacketReward receivedAt.
         * @member {number|Long} receivedAt
         * @memberof game.RedPacketReward
         * @instance
         */
        RedPacketReward.prototype.receivedAt = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new RedPacketReward instance using the specified properties.
         * @function create
         * @memberof game.RedPacketReward
         * @static
         * @param {game.IRedPacketReward=} [properties] Properties to set
         * @returns {game.RedPacketReward} RedPacketReward instance
         */
        RedPacketReward.create = function create(properties) {
            return new RedPacketReward(properties);
        };

        /**
         * Encodes the specified RedPacketReward message. Does not implicitly {@link game.RedPacketReward.verify|verify} messages.
         * @function encode
         * @memberof game.RedPacketReward
         * @static
         * @param {game.IRedPacketReward} message RedPacketReward message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketReward.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.packetId != null && Object.hasOwnProperty.call(message, "packetId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.packetId);
            if (message.receiverAddress != null && Object.hasOwnProperty.call(message, "receiverAddress"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.receiverAddress);
            if (message.amount != null && Object.hasOwnProperty.call(message, "amount"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.amount);
            if (message.receivedAt != null && Object.hasOwnProperty.call(message, "receivedAt"))
                writer.uint32(/* id 4, wireType 0 =*/32).int64(message.receivedAt);
            return writer;
        };

        /**
         * Encodes the specified RedPacketReward message, length delimited. Does not implicitly {@link game.RedPacketReward.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.RedPacketReward
         * @static
         * @param {game.IRedPacketReward} message RedPacketReward message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketReward.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a RedPacketReward message from the specified reader or buffer.
         * @function decode
         * @memberof game.RedPacketReward
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.RedPacketReward} RedPacketReward
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketReward.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.RedPacketReward();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.packetId = reader.string();
                        break;
                    }
                case 2: {
                        message.receiverAddress = reader.string();
                        break;
                    }
                case 3: {
                        message.amount = reader.float();
                        break;
                    }
                case 4: {
                        message.receivedAt = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a RedPacketReward message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.RedPacketReward
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.RedPacketReward} RedPacketReward
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketReward.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a RedPacketReward message.
         * @function verify
         * @memberof game.RedPacketReward
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        RedPacketReward.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.packetId != null && message.hasOwnProperty("packetId"))
                if (!$util.isString(message.packetId))
                    return "packetId: string expected";
            if (message.receiverAddress != null && message.hasOwnProperty("receiverAddress"))
                if (!$util.isString(message.receiverAddress))
                    return "receiverAddress: string expected";
            if (message.amount != null && message.hasOwnProperty("amount"))
                if (typeof message.amount !== "number")
                    return "amount: number expected";
            if (message.receivedAt != null && message.hasOwnProperty("receivedAt"))
                if (!$util.isInteger(message.receivedAt) && !(message.receivedAt && $util.isInteger(message.receivedAt.low) && $util.isInteger(message.receivedAt.high)))
                    return "receivedAt: integer|Long expected";
            return null;
        };

        /**
         * Creates a RedPacketReward message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.RedPacketReward
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.RedPacketReward} RedPacketReward
         */
        RedPacketReward.fromObject = function fromObject(object) {
            if (object instanceof $root.game.RedPacketReward)
                return object;
            var message = new $root.game.RedPacketReward();
            if (object.packetId != null)
                message.packetId = String(object.packetId);
            if (object.receiverAddress != null)
                message.receiverAddress = String(object.receiverAddress);
            if (object.amount != null)
                message.amount = Number(object.amount);
            if (object.receivedAt != null)
                if ($util.Long)
                    (message.receivedAt = $util.Long.fromValue(object.receivedAt)).unsigned = false;
                else if (typeof object.receivedAt === "string")
                    message.receivedAt = parseInt(object.receivedAt, 10);
                else if (typeof object.receivedAt === "number")
                    message.receivedAt = object.receivedAt;
                else if (typeof object.receivedAt === "object")
                    message.receivedAt = new $util.LongBits(object.receivedAt.low >>> 0, object.receivedAt.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a RedPacketReward message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.RedPacketReward
         * @static
         * @param {game.RedPacketReward} message RedPacketReward
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        RedPacketReward.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.packetId = "";
                object.receiverAddress = "";
                object.amount = 0;
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.receivedAt = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.receivedAt = options.longs === String ? "0" : 0;
            }
            if (message.packetId != null && message.hasOwnProperty("packetId"))
                object.packetId = message.packetId;
            if (message.receiverAddress != null && message.hasOwnProperty("receiverAddress"))
                object.receiverAddress = message.receiverAddress;
            if (message.amount != null && message.hasOwnProperty("amount"))
                object.amount = options.json && !isFinite(message.amount) ? String(message.amount) : message.amount;
            if (message.receivedAt != null && message.hasOwnProperty("receivedAt"))
                if (typeof message.receivedAt === "number")
                    object.receivedAt = options.longs === String ? String(message.receivedAt) : message.receivedAt;
                else
                    object.receivedAt = options.longs === String ? $util.Long.prototype.toString.call(message.receivedAt) : options.longs === Number ? new $util.LongBits(message.receivedAt.low >>> 0, message.receivedAt.high >>> 0).toNumber() : message.receivedAt;
            return object;
        };

        /**
         * Converts this RedPacketReward to JSON.
         * @function toJSON
         * @memberof game.RedPacketReward
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        RedPacketReward.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for RedPacketReward
         * @function getTypeUrl
         * @memberof game.RedPacketReward
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        RedPacketReward.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.RedPacketReward";
        };

        return RedPacketReward;
    })();

    game.ErrorMessage = (function() {

        /**
         * Properties of an ErrorMessage.
         * @memberof game
         * @interface IErrorMessage
         * @property {number|null} [code] ErrorMessage code
         * @property {string|null} [message] ErrorMessage message
         * @property {string|null} [details] ErrorMessage details
         */

        /**
         * Constructs a new ErrorMessage.
         * @memberof game
         * @classdesc Represents an ErrorMessage.
         * @implements IErrorMessage
         * @constructor
         * @param {game.IErrorMessage=} [properties] Properties to set
         */
        function ErrorMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ErrorMessage code.
         * @member {number} code
         * @memberof game.ErrorMessage
         * @instance
         */
        ErrorMessage.prototype.code = 0;

        /**
         * ErrorMessage message.
         * @member {string} message
         * @memberof game.ErrorMessage
         * @instance
         */
        ErrorMessage.prototype.message = "";

        /**
         * ErrorMessage details.
         * @member {string} details
         * @memberof game.ErrorMessage
         * @instance
         */
        ErrorMessage.prototype.details = "";

        /**
         * Creates a new ErrorMessage instance using the specified properties.
         * @function create
         * @memberof game.ErrorMessage
         * @static
         * @param {game.IErrorMessage=} [properties] Properties to set
         * @returns {game.ErrorMessage} ErrorMessage instance
         */
        ErrorMessage.create = function create(properties) {
            return new ErrorMessage(properties);
        };

        /**
         * Encodes the specified ErrorMessage message. Does not implicitly {@link game.ErrorMessage.verify|verify} messages.
         * @function encode
         * @memberof game.ErrorMessage
         * @static
         * @param {game.IErrorMessage} message ErrorMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ErrorMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.code != null && Object.hasOwnProperty.call(message, "code"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.code);
            if (message.message != null && Object.hasOwnProperty.call(message, "message"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.message);
            if (message.details != null && Object.hasOwnProperty.call(message, "details"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.details);
            return writer;
        };

        /**
         * Encodes the specified ErrorMessage message, length delimited. Does not implicitly {@link game.ErrorMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ErrorMessage
         * @static
         * @param {game.IErrorMessage} message ErrorMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ErrorMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an ErrorMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.ErrorMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ErrorMessage} ErrorMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ErrorMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ErrorMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.code = reader.int32();
                        break;
                    }
                case 2: {
                        message.message = reader.string();
                        break;
                    }
                case 3: {
                        message.details = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an ErrorMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ErrorMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ErrorMessage} ErrorMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ErrorMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an ErrorMessage message.
         * @function verify
         * @memberof game.ErrorMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ErrorMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.code != null && message.hasOwnProperty("code"))
                if (!$util.isInteger(message.code))
                    return "code: integer expected";
            if (message.message != null && message.hasOwnProperty("message"))
                if (!$util.isString(message.message))
                    return "message: string expected";
            if (message.details != null && message.hasOwnProperty("details"))
                if (!$util.isString(message.details))
                    return "details: string expected";
            return null;
        };

        /**
         * Creates an ErrorMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ErrorMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ErrorMessage} ErrorMessage
         */
        ErrorMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ErrorMessage)
                return object;
            var message = new $root.game.ErrorMessage();
            if (object.code != null)
                message.code = object.code | 0;
            if (object.message != null)
                message.message = String(object.message);
            if (object.details != null)
                message.details = String(object.details);
            return message;
        };

        /**
         * Creates a plain object from an ErrorMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ErrorMessage
         * @static
         * @param {game.ErrorMessage} message ErrorMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ErrorMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.code = 0;
                object.message = "";
                object.details = "";
            }
            if (message.code != null && message.hasOwnProperty("code"))
                object.code = message.code;
            if (message.message != null && message.hasOwnProperty("message"))
                object.message = message.message;
            if (message.details != null && message.hasOwnProperty("details"))
                object.details = message.details;
            return object;
        };

        /**
         * Converts this ErrorMessage to JSON.
         * @function toJSON
         * @memberof game.ErrorMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ErrorMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ErrorMessage
         * @function getTypeUrl
         * @memberof game.ErrorMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ErrorMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ErrorMessage";
        };

        return ErrorMessage;
    })();

    game.HeartbeatMessage = (function() {

        /**
         * Properties of a HeartbeatMessage.
         * @memberof game
         * @interface IHeartbeatMessage
         * @property {number|Long|null} [clientTimestamp] HeartbeatMessage clientTimestamp
         * @property {number|Long|null} [serverTimestamp] HeartbeatMessage serverTimestamp
         */

        /**
         * Constructs a new HeartbeatMessage.
         * @memberof game
         * @classdesc Represents a HeartbeatMessage.
         * @implements IHeartbeatMessage
         * @constructor
         * @param {game.IHeartbeatMessage=} [properties] Properties to set
         */
        function HeartbeatMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * HeartbeatMessage clientTimestamp.
         * @member {number|Long} clientTimestamp
         * @memberof game.HeartbeatMessage
         * @instance
         */
        HeartbeatMessage.prototype.clientTimestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * HeartbeatMessage serverTimestamp.
         * @member {number|Long} serverTimestamp
         * @memberof game.HeartbeatMessage
         * @instance
         */
        HeartbeatMessage.prototype.serverTimestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new HeartbeatMessage instance using the specified properties.
         * @function create
         * @memberof game.HeartbeatMessage
         * @static
         * @param {game.IHeartbeatMessage=} [properties] Properties to set
         * @returns {game.HeartbeatMessage} HeartbeatMessage instance
         */
        HeartbeatMessage.create = function create(properties) {
            return new HeartbeatMessage(properties);
        };

        /**
         * Encodes the specified HeartbeatMessage message. Does not implicitly {@link game.HeartbeatMessage.verify|verify} messages.
         * @function encode
         * @memberof game.HeartbeatMessage
         * @static
         * @param {game.IHeartbeatMessage} message HeartbeatMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        HeartbeatMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.clientTimestamp != null && Object.hasOwnProperty.call(message, "clientTimestamp"))
                writer.uint32(/* id 1, wireType 0 =*/8).int64(message.clientTimestamp);
            if (message.serverTimestamp != null && Object.hasOwnProperty.call(message, "serverTimestamp"))
                writer.uint32(/* id 2, wireType 0 =*/16).int64(message.serverTimestamp);
            return writer;
        };

        /**
         * Encodes the specified HeartbeatMessage message, length delimited. Does not implicitly {@link game.HeartbeatMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.HeartbeatMessage
         * @static
         * @param {game.IHeartbeatMessage} message HeartbeatMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        HeartbeatMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a HeartbeatMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.HeartbeatMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.HeartbeatMessage} HeartbeatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        HeartbeatMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.HeartbeatMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.clientTimestamp = reader.int64();
                        break;
                    }
                case 2: {
                        message.serverTimestamp = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a HeartbeatMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.HeartbeatMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.HeartbeatMessage} HeartbeatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        HeartbeatMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a HeartbeatMessage message.
         * @function verify
         * @memberof game.HeartbeatMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        HeartbeatMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.clientTimestamp != null && message.hasOwnProperty("clientTimestamp"))
                if (!$util.isInteger(message.clientTimestamp) && !(message.clientTimestamp && $util.isInteger(message.clientTimestamp.low) && $util.isInteger(message.clientTimestamp.high)))
                    return "clientTimestamp: integer|Long expected";
            if (message.serverTimestamp != null && message.hasOwnProperty("serverTimestamp"))
                if (!$util.isInteger(message.serverTimestamp) && !(message.serverTimestamp && $util.isInteger(message.serverTimestamp.low) && $util.isInteger(message.serverTimestamp.high)))
                    return "serverTimestamp: integer|Long expected";
            return null;
        };

        /**
         * Creates a HeartbeatMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.HeartbeatMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.HeartbeatMessage} HeartbeatMessage
         */
        HeartbeatMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.HeartbeatMessage)
                return object;
            var message = new $root.game.HeartbeatMessage();
            if (object.clientTimestamp != null)
                if ($util.Long)
                    (message.clientTimestamp = $util.Long.fromValue(object.clientTimestamp)).unsigned = false;
                else if (typeof object.clientTimestamp === "string")
                    message.clientTimestamp = parseInt(object.clientTimestamp, 10);
                else if (typeof object.clientTimestamp === "number")
                    message.clientTimestamp = object.clientTimestamp;
                else if (typeof object.clientTimestamp === "object")
                    message.clientTimestamp = new $util.LongBits(object.clientTimestamp.low >>> 0, object.clientTimestamp.high >>> 0).toNumber();
            if (object.serverTimestamp != null)
                if ($util.Long)
                    (message.serverTimestamp = $util.Long.fromValue(object.serverTimestamp)).unsigned = false;
                else if (typeof object.serverTimestamp === "string")
                    message.serverTimestamp = parseInt(object.serverTimestamp, 10);
                else if (typeof object.serverTimestamp === "number")
                    message.serverTimestamp = object.serverTimestamp;
                else if (typeof object.serverTimestamp === "object")
                    message.serverTimestamp = new $util.LongBits(object.serverTimestamp.low >>> 0, object.serverTimestamp.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a HeartbeatMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.HeartbeatMessage
         * @static
         * @param {game.HeartbeatMessage} message HeartbeatMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        HeartbeatMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.clientTimestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.clientTimestamp = options.longs === String ? "0" : 0;
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.serverTimestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.serverTimestamp = options.longs === String ? "0" : 0;
            }
            if (message.clientTimestamp != null && message.hasOwnProperty("clientTimestamp"))
                if (typeof message.clientTimestamp === "number")
                    object.clientTimestamp = options.longs === String ? String(message.clientTimestamp) : message.clientTimestamp;
                else
                    object.clientTimestamp = options.longs === String ? $util.Long.prototype.toString.call(message.clientTimestamp) : options.longs === Number ? new $util.LongBits(message.clientTimestamp.low >>> 0, message.clientTimestamp.high >>> 0).toNumber() : message.clientTimestamp;
            if (message.serverTimestamp != null && message.hasOwnProperty("serverTimestamp"))
                if (typeof message.serverTimestamp === "number")
                    object.serverTimestamp = options.longs === String ? String(message.serverTimestamp) : message.serverTimestamp;
                else
                    object.serverTimestamp = options.longs === String ? $util.Long.prototype.toString.call(message.serverTimestamp) : options.longs === Number ? new $util.LongBits(message.serverTimestamp.low >>> 0, message.serverTimestamp.high >>> 0).toNumber() : message.serverTimestamp;
            return object;
        };

        /**
         * Converts this HeartbeatMessage to JSON.
         * @function toJSON
         * @memberof game.HeartbeatMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        HeartbeatMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for HeartbeatMessage
         * @function getTypeUrl
         * @memberof game.HeartbeatMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        HeartbeatMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.HeartbeatMessage";
        };

        return HeartbeatMessage;
    })();

    game.LoginRequest = (function() {

        /**
         * Properties of a LoginRequest.
         * @memberof game
         * @interface ILoginRequest
         * @property {string|null} [btcAddress] LoginRequest btcAddress
         * @property {string|null} [sessionId] LoginRequest sessionId
         */

        /**
         * Constructs a new LoginRequest.
         * @memberof game
         * @classdesc Represents a LoginRequest.
         * @implements ILoginRequest
         * @constructor
         * @param {game.ILoginRequest=} [properties] Properties to set
         */
        function LoginRequest(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * LoginRequest btcAddress.
         * @member {string} btcAddress
         * @memberof game.LoginRequest
         * @instance
         */
        LoginRequest.prototype.btcAddress = "";

        /**
         * LoginRequest sessionId.
         * @member {string} sessionId
         * @memberof game.LoginRequest
         * @instance
         */
        LoginRequest.prototype.sessionId = "";

        /**
         * Creates a new LoginRequest instance using the specified properties.
         * @function create
         * @memberof game.LoginRequest
         * @static
         * @param {game.ILoginRequest=} [properties] Properties to set
         * @returns {game.LoginRequest} LoginRequest instance
         */
        LoginRequest.create = function create(properties) {
            return new LoginRequest(properties);
        };

        /**
         * Encodes the specified LoginRequest message. Does not implicitly {@link game.LoginRequest.verify|verify} messages.
         * @function encode
         * @memberof game.LoginRequest
         * @static
         * @param {game.ILoginRequest} message LoginRequest message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        LoginRequest.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            if (message.sessionId != null && Object.hasOwnProperty.call(message, "sessionId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.sessionId);
            return writer;
        };

        /**
         * Encodes the specified LoginRequest message, length delimited. Does not implicitly {@link game.LoginRequest.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.LoginRequest
         * @static
         * @param {game.ILoginRequest} message LoginRequest message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        LoginRequest.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a LoginRequest message from the specified reader or buffer.
         * @function decode
         * @memberof game.LoginRequest
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.LoginRequest} LoginRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        LoginRequest.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.LoginRequest();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.sessionId = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a LoginRequest message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.LoginRequest
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.LoginRequest} LoginRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        LoginRequest.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a LoginRequest message.
         * @function verify
         * @memberof game.LoginRequest
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        LoginRequest.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.sessionId != null && message.hasOwnProperty("sessionId"))
                if (!$util.isString(message.sessionId))
                    return "sessionId: string expected";
            return null;
        };

        /**
         * Creates a LoginRequest message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.LoginRequest
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.LoginRequest} LoginRequest
         */
        LoginRequest.fromObject = function fromObject(object) {
            if (object instanceof $root.game.LoginRequest)
                return object;
            var message = new $root.game.LoginRequest();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.sessionId != null)
                message.sessionId = String(object.sessionId);
            return message;
        };

        /**
         * Creates a plain object from a LoginRequest message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.LoginRequest
         * @static
         * @param {game.LoginRequest} message LoginRequest
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        LoginRequest.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.btcAddress = "";
                object.sessionId = "";
            }
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.sessionId != null && message.hasOwnProperty("sessionId"))
                object.sessionId = message.sessionId;
            return object;
        };

        /**
         * Converts this LoginRequest to JSON.
         * @function toJSON
         * @memberof game.LoginRequest
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        LoginRequest.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for LoginRequest
         * @function getTypeUrl
         * @memberof game.LoginRequest
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        LoginRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.LoginRequest";
        };

        return LoginRequest;
    })();

    game.LoginResponse = (function() {

        /**
         * Properties of a LoginResponse.
         * @memberof game
         * @interface ILoginResponse
         * @property {boolean|null} [success] LoginResponse success
         * @property {string|null} [message] LoginResponse message
         * @property {number|Long|null} [serverTime] LoginResponse serverTime
         */

        /**
         * Constructs a new LoginResponse.
         * @memberof game
         * @classdesc Represents a LoginResponse.
         * @implements ILoginResponse
         * @constructor
         * @param {game.ILoginResponse=} [properties] Properties to set
         */
        function LoginResponse(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * LoginResponse success.
         * @member {boolean} success
         * @memberof game.LoginResponse
         * @instance
         */
        LoginResponse.prototype.success = false;

        /**
         * LoginResponse message.
         * @member {string} message
         * @memberof game.LoginResponse
         * @instance
         */
        LoginResponse.prototype.message = "";

        /**
         * LoginResponse serverTime.
         * @member {number|Long} serverTime
         * @memberof game.LoginResponse
         * @instance
         */
        LoginResponse.prototype.serverTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new LoginResponse instance using the specified properties.
         * @function create
         * @memberof game.LoginResponse
         * @static
         * @param {game.ILoginResponse=} [properties] Properties to set
         * @returns {game.LoginResponse} LoginResponse instance
         */
        LoginResponse.create = function create(properties) {
            return new LoginResponse(properties);
        };

        /**
         * Encodes the specified LoginResponse message. Does not implicitly {@link game.LoginResponse.verify|verify} messages.
         * @function encode
         * @memberof game.LoginResponse
         * @static
         * @param {game.ILoginResponse} message LoginResponse message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        LoginResponse.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.success != null && Object.hasOwnProperty.call(message, "success"))
                writer.uint32(/* id 1, wireType 0 =*/8).bool(message.success);
            if (message.message != null && Object.hasOwnProperty.call(message, "message"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.message);
            if (message.serverTime != null && Object.hasOwnProperty.call(message, "serverTime"))
                writer.uint32(/* id 3, wireType 0 =*/24).int64(message.serverTime);
            return writer;
        };

        /**
         * Encodes the specified LoginResponse message, length delimited. Does not implicitly {@link game.LoginResponse.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.LoginResponse
         * @static
         * @param {game.ILoginResponse} message LoginResponse message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        LoginResponse.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a LoginResponse message from the specified reader or buffer.
         * @function decode
         * @memberof game.LoginResponse
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.LoginResponse} LoginResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        LoginResponse.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.LoginResponse();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.success = reader.bool();
                        break;
                    }
                case 2: {
                        message.message = reader.string();
                        break;
                    }
                case 3: {
                        message.serverTime = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a LoginResponse message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.LoginResponse
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.LoginResponse} LoginResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        LoginResponse.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a LoginResponse message.
         * @function verify
         * @memberof game.LoginResponse
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        LoginResponse.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.success != null && message.hasOwnProperty("success"))
                if (typeof message.success !== "boolean")
                    return "success: boolean expected";
            if (message.message != null && message.hasOwnProperty("message"))
                if (!$util.isString(message.message))
                    return "message: string expected";
            if (message.serverTime != null && message.hasOwnProperty("serverTime"))
                if (!$util.isInteger(message.serverTime) && !(message.serverTime && $util.isInteger(message.serverTime.low) && $util.isInteger(message.serverTime.high)))
                    return "serverTime: integer|Long expected";
            return null;
        };

        /**
         * Creates a LoginResponse message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.LoginResponse
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.LoginResponse} LoginResponse
         */
        LoginResponse.fromObject = function fromObject(object) {
            if (object instanceof $root.game.LoginResponse)
                return object;
            var message = new $root.game.LoginResponse();
            if (object.success != null)
                message.success = Boolean(object.success);
            if (object.message != null)
                message.message = String(object.message);
            if (object.serverTime != null)
                if ($util.Long)
                    (message.serverTime = $util.Long.fromValue(object.serverTime)).unsigned = false;
                else if (typeof object.serverTime === "string")
                    message.serverTime = parseInt(object.serverTime, 10);
                else if (typeof object.serverTime === "number")
                    message.serverTime = object.serverTime;
                else if (typeof object.serverTime === "object")
                    message.serverTime = new $util.LongBits(object.serverTime.low >>> 0, object.serverTime.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a LoginResponse message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.LoginResponse
         * @static
         * @param {game.LoginResponse} message LoginResponse
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        LoginResponse.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.success = false;
                object.message = "";
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.serverTime = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.serverTime = options.longs === String ? "0" : 0;
            }
            if (message.success != null && message.hasOwnProperty("success"))
                object.success = message.success;
            if (message.message != null && message.hasOwnProperty("message"))
                object.message = message.message;
            if (message.serverTime != null && message.hasOwnProperty("serverTime"))
                if (typeof message.serverTime === "number")
                    object.serverTime = options.longs === String ? String(message.serverTime) : message.serverTime;
                else
                    object.serverTime = options.longs === String ? $util.Long.prototype.toString.call(message.serverTime) : options.longs === Number ? new $util.LongBits(message.serverTime.low >>> 0, message.serverTime.high >>> 0).toNumber() : message.serverTime;
            return object;
        };

        /**
         * Converts this LoginResponse to JSON.
         * @function toJSON
         * @memberof game.LoginResponse
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        LoginResponse.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for LoginResponse
         * @function getTypeUrl
         * @memberof game.LoginResponse
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        LoginResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.LoginResponse";
        };

        return LoginResponse;
    })();

    game.RoomListRequest = (function() {

        /**
         * Properties of a RoomListRequest.
         * @memberof game
         * @interface IRoomListRequest
         * @property {number|null} [page] RoomListRequest page
         * @property {number|null} [limit] RoomListRequest limit
         * @property {string|null} [filter] RoomListRequest filter
         */

        /**
         * Constructs a new RoomListRequest.
         * @memberof game
         * @classdesc Represents a RoomListRequest.
         * @implements IRoomListRequest
         * @constructor
         * @param {game.IRoomListRequest=} [properties] Properties to set
         */
        function RoomListRequest(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * RoomListRequest page.
         * @member {number} page
         * @memberof game.RoomListRequest
         * @instance
         */
        RoomListRequest.prototype.page = 0;

        /**
         * RoomListRequest limit.
         * @member {number} limit
         * @memberof game.RoomListRequest
         * @instance
         */
        RoomListRequest.prototype.limit = 0;

        /**
         * RoomListRequest filter.
         * @member {string} filter
         * @memberof game.RoomListRequest
         * @instance
         */
        RoomListRequest.prototype.filter = "";

        /**
         * Creates a new RoomListRequest instance using the specified properties.
         * @function create
         * @memberof game.RoomListRequest
         * @static
         * @param {game.IRoomListRequest=} [properties] Properties to set
         * @returns {game.RoomListRequest} RoomListRequest instance
         */
        RoomListRequest.create = function create(properties) {
            return new RoomListRequest(properties);
        };

        /**
         * Encodes the specified RoomListRequest message. Does not implicitly {@link game.RoomListRequest.verify|verify} messages.
         * @function encode
         * @memberof game.RoomListRequest
         * @static
         * @param {game.IRoomListRequest} message RoomListRequest message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RoomListRequest.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.page != null && Object.hasOwnProperty.call(message, "page"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.page);
            if (message.limit != null && Object.hasOwnProperty.call(message, "limit"))
                writer.uint32(/* id 2, wireType 0 =*/16).int32(message.limit);
            if (message.filter != null && Object.hasOwnProperty.call(message, "filter"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.filter);
            return writer;
        };

        /**
         * Encodes the specified RoomListRequest message, length delimited. Does not implicitly {@link game.RoomListRequest.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.RoomListRequest
         * @static
         * @param {game.IRoomListRequest} message RoomListRequest message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RoomListRequest.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a RoomListRequest message from the specified reader or buffer.
         * @function decode
         * @memberof game.RoomListRequest
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.RoomListRequest} RoomListRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RoomListRequest.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.RoomListRequest();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.page = reader.int32();
                        break;
                    }
                case 2: {
                        message.limit = reader.int32();
                        break;
                    }
                case 3: {
                        message.filter = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a RoomListRequest message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.RoomListRequest
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.RoomListRequest} RoomListRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RoomListRequest.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a RoomListRequest message.
         * @function verify
         * @memberof game.RoomListRequest
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        RoomListRequest.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.page != null && message.hasOwnProperty("page"))
                if (!$util.isInteger(message.page))
                    return "page: integer expected";
            if (message.limit != null && message.hasOwnProperty("limit"))
                if (!$util.isInteger(message.limit))
                    return "limit: integer expected";
            if (message.filter != null && message.hasOwnProperty("filter"))
                if (!$util.isString(message.filter))
                    return "filter: string expected";
            return null;
        };

        /**
         * Creates a RoomListRequest message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.RoomListRequest
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.RoomListRequest} RoomListRequest
         */
        RoomListRequest.fromObject = function fromObject(object) {
            if (object instanceof $root.game.RoomListRequest)
                return object;
            var message = new $root.game.RoomListRequest();
            if (object.page != null)
                message.page = object.page | 0;
            if (object.limit != null)
                message.limit = object.limit | 0;
            if (object.filter != null)
                message.filter = String(object.filter);
            return message;
        };

        /**
         * Creates a plain object from a RoomListRequest message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.RoomListRequest
         * @static
         * @param {game.RoomListRequest} message RoomListRequest
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        RoomListRequest.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.page = 0;
                object.limit = 0;
                object.filter = "";
            }
            if (message.page != null && message.hasOwnProperty("page"))
                object.page = message.page;
            if (message.limit != null && message.hasOwnProperty("limit"))
                object.limit = message.limit;
            if (message.filter != null && message.hasOwnProperty("filter"))
                object.filter = message.filter;
            return object;
        };

        /**
         * Converts this RoomListRequest to JSON.
         * @function toJSON
         * @memberof game.RoomListRequest
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        RoomListRequest.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for RoomListRequest
         * @function getTypeUrl
         * @memberof game.RoomListRequest
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        RoomListRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.RoomListRequest";
        };

        return RoomListRequest;
    })();

    game.RoomInfo = (function() {

        /**
         * Properties of a RoomInfo.
         * @memberof game
         * @interface IRoomInfo
         * @property {string|null} [roomId] RoomInfo roomId
         * @property {string|null} [name] RoomInfo name
         * @property {number|null} [currentPlayers] RoomInfo currentPlayers
         * @property {number|null} [maxPlayers] RoomInfo maxPlayers
         * @property {string|null} [status] RoomInfo status
         * @property {string|null} [mapId] RoomInfo mapId
         */

        /**
         * Constructs a new RoomInfo.
         * @memberof game
         * @classdesc Represents a RoomInfo.
         * @implements IRoomInfo
         * @constructor
         * @param {game.IRoomInfo=} [properties] Properties to set
         */
        function RoomInfo(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * RoomInfo roomId.
         * @member {string} roomId
         * @memberof game.RoomInfo
         * @instance
         */
        RoomInfo.prototype.roomId = "";

        /**
         * RoomInfo name.
         * @member {string} name
         * @memberof game.RoomInfo
         * @instance
         */
        RoomInfo.prototype.name = "";

        /**
         * RoomInfo currentPlayers.
         * @member {number} currentPlayers
         * @memberof game.RoomInfo
         * @instance
         */
        RoomInfo.prototype.currentPlayers = 0;

        /**
         * RoomInfo maxPlayers.
         * @member {number} maxPlayers
         * @memberof game.RoomInfo
         * @instance
         */
        RoomInfo.prototype.maxPlayers = 0;

        /**
         * RoomInfo status.
         * @member {string} status
         * @memberof game.RoomInfo
         * @instance
         */
        RoomInfo.prototype.status = "";

        /**
         * RoomInfo mapId.
         * @member {string} mapId
         * @memberof game.RoomInfo
         * @instance
         */
        RoomInfo.prototype.mapId = "";

        /**
         * Creates a new RoomInfo instance using the specified properties.
         * @function create
         * @memberof game.RoomInfo
         * @static
         * @param {game.IRoomInfo=} [properties] Properties to set
         * @returns {game.RoomInfo} RoomInfo instance
         */
        RoomInfo.create = function create(properties) {
            return new RoomInfo(properties);
        };

        /**
         * Encodes the specified RoomInfo message. Does not implicitly {@link game.RoomInfo.verify|verify} messages.
         * @function encode
         * @memberof game.RoomInfo
         * @static
         * @param {game.IRoomInfo} message RoomInfo message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RoomInfo.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.roomId != null && Object.hasOwnProperty.call(message, "roomId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.roomId);
            if (message.name != null && Object.hasOwnProperty.call(message, "name"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.name);
            if (message.currentPlayers != null && Object.hasOwnProperty.call(message, "currentPlayers"))
                writer.uint32(/* id 3, wireType 0 =*/24).int32(message.currentPlayers);
            if (message.maxPlayers != null && Object.hasOwnProperty.call(message, "maxPlayers"))
                writer.uint32(/* id 4, wireType 0 =*/32).int32(message.maxPlayers);
            if (message.status != null && Object.hasOwnProperty.call(message, "status"))
                writer.uint32(/* id 5, wireType 2 =*/42).string(message.status);
            if (message.mapId != null && Object.hasOwnProperty.call(message, "mapId"))
                writer.uint32(/* id 6, wireType 2 =*/50).string(message.mapId);
            return writer;
        };

        /**
         * Encodes the specified RoomInfo message, length delimited. Does not implicitly {@link game.RoomInfo.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.RoomInfo
         * @static
         * @param {game.IRoomInfo} message RoomInfo message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RoomInfo.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a RoomInfo message from the specified reader or buffer.
         * @function decode
         * @memberof game.RoomInfo
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.RoomInfo} RoomInfo
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RoomInfo.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.RoomInfo();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.roomId = reader.string();
                        break;
                    }
                case 2: {
                        message.name = reader.string();
                        break;
                    }
                case 3: {
                        message.currentPlayers = reader.int32();
                        break;
                    }
                case 4: {
                        message.maxPlayers = reader.int32();
                        break;
                    }
                case 5: {
                        message.status = reader.string();
                        break;
                    }
                case 6: {
                        message.mapId = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a RoomInfo message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.RoomInfo
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.RoomInfo} RoomInfo
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RoomInfo.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a RoomInfo message.
         * @function verify
         * @memberof game.RoomInfo
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        RoomInfo.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.roomId != null && message.hasOwnProperty("roomId"))
                if (!$util.isString(message.roomId))
                    return "roomId: string expected";
            if (message.name != null && message.hasOwnProperty("name"))
                if (!$util.isString(message.name))
                    return "name: string expected";
            if (message.currentPlayers != null && message.hasOwnProperty("currentPlayers"))
                if (!$util.isInteger(message.currentPlayers))
                    return "currentPlayers: integer expected";
            if (message.maxPlayers != null && message.hasOwnProperty("maxPlayers"))
                if (!$util.isInteger(message.maxPlayers))
                    return "maxPlayers: integer expected";
            if (message.status != null && message.hasOwnProperty("status"))
                if (!$util.isString(message.status))
                    return "status: string expected";
            if (message.mapId != null && message.hasOwnProperty("mapId"))
                if (!$util.isString(message.mapId))
                    return "mapId: string expected";
            return null;
        };

        /**
         * Creates a RoomInfo message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.RoomInfo
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.RoomInfo} RoomInfo
         */
        RoomInfo.fromObject = function fromObject(object) {
            if (object instanceof $root.game.RoomInfo)
                return object;
            var message = new $root.game.RoomInfo();
            if (object.roomId != null)
                message.roomId = String(object.roomId);
            if (object.name != null)
                message.name = String(object.name);
            if (object.currentPlayers != null)
                message.currentPlayers = object.currentPlayers | 0;
            if (object.maxPlayers != null)
                message.maxPlayers = object.maxPlayers | 0;
            if (object.status != null)
                message.status = String(object.status);
            if (object.mapId != null)
                message.mapId = String(object.mapId);
            return message;
        };

        /**
         * Creates a plain object from a RoomInfo message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.RoomInfo
         * @static
         * @param {game.RoomInfo} message RoomInfo
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        RoomInfo.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.roomId = "";
                object.name = "";
                object.currentPlayers = 0;
                object.maxPlayers = 0;
                object.status = "";
                object.mapId = "";
            }
            if (message.roomId != null && message.hasOwnProperty("roomId"))
                object.roomId = message.roomId;
            if (message.name != null && message.hasOwnProperty("name"))
                object.name = message.name;
            if (message.currentPlayers != null && message.hasOwnProperty("currentPlayers"))
                object.currentPlayers = message.currentPlayers;
            if (message.maxPlayers != null && message.hasOwnProperty("maxPlayers"))
                object.maxPlayers = message.maxPlayers;
            if (message.status != null && message.hasOwnProperty("status"))
                object.status = message.status;
            if (message.mapId != null && message.hasOwnProperty("mapId"))
                object.mapId = message.mapId;
            return object;
        };

        /**
         * Converts this RoomInfo to JSON.
         * @function toJSON
         * @memberof game.RoomInfo
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        RoomInfo.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for RoomInfo
         * @function getTypeUrl
         * @memberof game.RoomInfo
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        RoomInfo.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.RoomInfo";
        };

        return RoomInfo;
    })();

    game.RoomListResponse = (function() {

        /**
         * Properties of a RoomListResponse.
         * @memberof game
         * @interface IRoomListResponse
         * @property {Array.<game.IRoomInfo>|null} [rooms] RoomListResponse rooms
         * @property {number|null} [totalCount] RoomListResponse totalCount
         * @property {number|null} [page] RoomListResponse page
         */

        /**
         * Constructs a new RoomListResponse.
         * @memberof game
         * @classdesc Represents a RoomListResponse.
         * @implements IRoomListResponse
         * @constructor
         * @param {game.IRoomListResponse=} [properties] Properties to set
         */
        function RoomListResponse(properties) {
            this.rooms = [];
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * RoomListResponse rooms.
         * @member {Array.<game.IRoomInfo>} rooms
         * @memberof game.RoomListResponse
         * @instance
         */
        RoomListResponse.prototype.rooms = $util.emptyArray;

        /**
         * RoomListResponse totalCount.
         * @member {number} totalCount
         * @memberof game.RoomListResponse
         * @instance
         */
        RoomListResponse.prototype.totalCount = 0;

        /**
         * RoomListResponse page.
         * @member {number} page
         * @memberof game.RoomListResponse
         * @instance
         */
        RoomListResponse.prototype.page = 0;

        /**
         * Creates a new RoomListResponse instance using the specified properties.
         * @function create
         * @memberof game.RoomListResponse
         * @static
         * @param {game.IRoomListResponse=} [properties] Properties to set
         * @returns {game.RoomListResponse} RoomListResponse instance
         */
        RoomListResponse.create = function create(properties) {
            return new RoomListResponse(properties);
        };

        /**
         * Encodes the specified RoomListResponse message. Does not implicitly {@link game.RoomListResponse.verify|verify} messages.
         * @function encode
         * @memberof game.RoomListResponse
         * @static
         * @param {game.IRoomListResponse} message RoomListResponse message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RoomListResponse.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.rooms != null && message.rooms.length)
                for (var i = 0; i < message.rooms.length; ++i)
                    $root.game.RoomInfo.encode(message.rooms[i], writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();
            if (message.totalCount != null && Object.hasOwnProperty.call(message, "totalCount"))
                writer.uint32(/* id 2, wireType 0 =*/16).int32(message.totalCount);
            if (message.page != null && Object.hasOwnProperty.call(message, "page"))
                writer.uint32(/* id 3, wireType 0 =*/24).int32(message.page);
            return writer;
        };

        /**
         * Encodes the specified RoomListResponse message, length delimited. Does not implicitly {@link game.RoomListResponse.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.RoomListResponse
         * @static
         * @param {game.IRoomListResponse} message RoomListResponse message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RoomListResponse.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a RoomListResponse message from the specified reader or buffer.
         * @function decode
         * @memberof game.RoomListResponse
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.RoomListResponse} RoomListResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RoomListResponse.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.RoomListResponse();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        if (!(message.rooms && message.rooms.length))
                            message.rooms = [];
                        message.rooms.push($root.game.RoomInfo.decode(reader, reader.uint32()));
                        break;
                    }
                case 2: {
                        message.totalCount = reader.int32();
                        break;
                    }
                case 3: {
                        message.page = reader.int32();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a RoomListResponse message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.RoomListResponse
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.RoomListResponse} RoomListResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RoomListResponse.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a RoomListResponse message.
         * @function verify
         * @memberof game.RoomListResponse
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        RoomListResponse.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.rooms != null && message.hasOwnProperty("rooms")) {
                if (!Array.isArray(message.rooms))
                    return "rooms: array expected";
                for (var i = 0; i < message.rooms.length; ++i) {
                    var error = $root.game.RoomInfo.verify(message.rooms[i]);
                    if (error)
                        return "rooms." + error;
                }
            }
            if (message.totalCount != null && message.hasOwnProperty("totalCount"))
                if (!$util.isInteger(message.totalCount))
                    return "totalCount: integer expected";
            if (message.page != null && message.hasOwnProperty("page"))
                if (!$util.isInteger(message.page))
                    return "page: integer expected";
            return null;
        };

        /**
         * Creates a RoomListResponse message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.RoomListResponse
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.RoomListResponse} RoomListResponse
         */
        RoomListResponse.fromObject = function fromObject(object) {
            if (object instanceof $root.game.RoomListResponse)
                return object;
            var message = new $root.game.RoomListResponse();
            if (object.rooms) {
                if (!Array.isArray(object.rooms))
                    throw TypeError(".game.RoomListResponse.rooms: array expected");
                message.rooms = [];
                for (var i = 0; i < object.rooms.length; ++i) {
                    if (typeof object.rooms[i] !== "object")
                        throw TypeError(".game.RoomListResponse.rooms: object expected");
                    message.rooms[i] = $root.game.RoomInfo.fromObject(object.rooms[i]);
                }
            }
            if (object.totalCount != null)
                message.totalCount = object.totalCount | 0;
            if (object.page != null)
                message.page = object.page | 0;
            return message;
        };

        /**
         * Creates a plain object from a RoomListResponse message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.RoomListResponse
         * @static
         * @param {game.RoomListResponse} message RoomListResponse
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        RoomListResponse.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.arrays || options.defaults)
                object.rooms = [];
            if (options.defaults) {
                object.totalCount = 0;
                object.page = 0;
            }
            if (message.rooms && message.rooms.length) {
                object.rooms = [];
                for (var j = 0; j < message.rooms.length; ++j)
                    object.rooms[j] = $root.game.RoomInfo.toObject(message.rooms[j], options);
            }
            if (message.totalCount != null && message.hasOwnProperty("totalCount"))
                object.totalCount = message.totalCount;
            if (message.page != null && message.hasOwnProperty("page"))
                object.page = message.page;
            return object;
        };

        /**
         * Converts this RoomListResponse to JSON.
         * @function toJSON
         * @memberof game.RoomListResponse
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        RoomListResponse.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for RoomListResponse
         * @function getTypeUrl
         * @memberof game.RoomListResponse
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        RoomListResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.RoomListResponse";
        };

        return RoomListResponse;
    })();

    return game;
})();

module.exports = $root;
