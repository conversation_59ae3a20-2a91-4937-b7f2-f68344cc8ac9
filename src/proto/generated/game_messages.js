/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
"use strict";

var $protobuf = require("protobufjs/minimal");

// Common aliases
var $Reader = $protobuf.Reader, $Writer = $protobuf.Writer, $util = $protobuf.util;

// Exported root namespace
var $root = $protobuf.roots["default"] || ($protobuf.roots["default"] = {});

$root.game = (function() {

    /**
     * Namespace game.
     * @exports game
     * @namespace
     */
    var game = {};

    /**
     * PacketType enum.
     * @name game.PacketType
     * @enum {number}
     * @property {number} PLAYER_NONE=0 PLAYER_NONE value
     * @property {number} PLAYER_ENTER=1 PLAYER_ENTER value
     * @property {number} PLAYER_LEAVE=2 PLAYER_LEAVE value
     * @property {number} PLAYER_POSITION=10 PLAYER_POSITION value
     * @property {number} PLAYER_ANIMATION=11 PLAYER_ANIMATION value
     * @property {number} PLAYER_UPDATE=12 PLAYER_UPDATE value
     * @property {number} PET_POSITION=50 PET_POSITION value
     * @property {number} PET_ANIMATION=51 PET_ANIMATION value
     * @property {number} CHAT_ENTER=100 CHAT_ENTER value
     * @property {number} CHAT_LEAVE=101 CHAT_LEAVE value
     * @property {number} CHAT_MESSAGE=102 CHAT_MESSAGE value
     * @property {number} RED_PACKET_UPDATE=201 RED_PACKET_UPDATE value
     * @property {number} RED_PACKET_REWARD=202 RED_PACKET_REWARD value
     */
    game.PacketType = (function() {
        var valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "PLAYER_NONE"] = 0;
        values[valuesById[1] = "PLAYER_ENTER"] = 1;
        values[valuesById[2] = "PLAYER_LEAVE"] = 2;
        values[valuesById[10] = "PLAYER_POSITION"] = 10;
        values[valuesById[11] = "PLAYER_ANIMATION"] = 11;
        values[valuesById[12] = "PLAYER_UPDATE"] = 12;
        values[valuesById[50] = "PET_POSITION"] = 50;
        values[valuesById[51] = "PET_ANIMATION"] = 51;
        values[valuesById[100] = "CHAT_ENTER"] = 100;
        values[valuesById[101] = "CHAT_LEAVE"] = 101;
        values[valuesById[102] = "CHAT_MESSAGE"] = 102;
        values[valuesById[201] = "RED_PACKET_UPDATE"] = 201;
        values[valuesById[202] = "RED_PACKET_REWARD"] = 202;
        return values;
    })();

    game.GameMessage = (function() {

        /**
         * Properties of a GameMessage.
         * @memberof game
         * @interface IGameMessage
         * @property {game.PacketType|null} [packetType] GameMessage packetType
         * @property {Uint8Array|null} [data] GameMessage data
         * @property {number|Long|null} [timestamp] GameMessage timestamp
         */

        /**
         * Constructs a new GameMessage.
         * @memberof game
         * @classdesc Represents a GameMessage.
         * @implements IGameMessage
         * @constructor
         * @param {game.IGameMessage=} [properties] Properties to set
         */
        function GameMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * GameMessage packetType.
         * @member {game.PacketType} packetType
         * @memberof game.GameMessage
         * @instance
         */
        GameMessage.prototype.packetType = 0;

        /**
         * GameMessage data.
         * @member {Uint8Array} data
         * @memberof game.GameMessage
         * @instance
         */
        GameMessage.prototype.data = $util.newBuffer([]);

        /**
         * GameMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof game.GameMessage
         * @instance
         */
        GameMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new GameMessage instance using the specified properties.
         * @function create
         * @memberof game.GameMessage
         * @static
         * @param {game.IGameMessage=} [properties] Properties to set
         * @returns {game.GameMessage} GameMessage instance
         */
        GameMessage.create = function create(properties) {
            return new GameMessage(properties);
        };

        /**
         * Encodes the specified GameMessage message. Does not implicitly {@link game.GameMessage.verify|verify} messages.
         * @function encode
         * @memberof game.GameMessage
         * @static
         * @param {game.IGameMessage} message GameMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        GameMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.packetType != null && Object.hasOwnProperty.call(message, "packetType"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.packetType);
            if (message.data != null && Object.hasOwnProperty.call(message, "data"))
                writer.uint32(/* id 2, wireType 2 =*/18).bytes(message.data);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 3, wireType 0 =*/24).int64(message.timestamp);
            return writer;
        };

        /**
         * Encodes the specified GameMessage message, length delimited. Does not implicitly {@link game.GameMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.GameMessage
         * @static
         * @param {game.IGameMessage} message GameMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        GameMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a GameMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.GameMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.GameMessage} GameMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        GameMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.GameMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.packetType = reader.int32();
                        break;
                    }
                case 2: {
                        message.data = reader.bytes();
                        break;
                    }
                case 3: {
                        message.timestamp = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a GameMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.GameMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.GameMessage} GameMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        GameMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a GameMessage message.
         * @function verify
         * @memberof game.GameMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        GameMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.packetType != null && message.hasOwnProperty("packetType"))
                switch (message.packetType) {
                default:
                    return "packetType: enum value expected";
                case 0:
                case 1:
                case 2:
                case 10:
                case 11:
                case 12:
                case 50:
                case 51:
                case 100:
                case 101:
                case 102:
                case 201:
                case 202:
                    break;
                }
            if (message.data != null && message.hasOwnProperty("data"))
                if (!(message.data && typeof message.data.length === "number" || $util.isString(message.data)))
                    return "data: buffer expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            return null;
        };

        /**
         * Creates a GameMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.GameMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.GameMessage} GameMessage
         */
        GameMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.GameMessage)
                return object;
            var message = new $root.game.GameMessage();
            switch (object.packetType) {
            default:
                if (typeof object.packetType === "number") {
                    message.packetType = object.packetType;
                    break;
                }
                break;
            case "PLAYER_NONE":
            case 0:
                message.packetType = 0;
                break;
            case "PLAYER_ENTER":
            case 1:
                message.packetType = 1;
                break;
            case "PLAYER_LEAVE":
            case 2:
                message.packetType = 2;
                break;
            case "PLAYER_POSITION":
            case 10:
                message.packetType = 10;
                break;
            case "PLAYER_ANIMATION":
            case 11:
                message.packetType = 11;
                break;
            case "PLAYER_UPDATE":
            case 12:
                message.packetType = 12;
                break;
            case "PET_POSITION":
            case 50:
                message.packetType = 50;
                break;
            case "PET_ANIMATION":
            case 51:
                message.packetType = 51;
                break;
            case "CHAT_ENTER":
            case 100:
                message.packetType = 100;
                break;
            case "CHAT_LEAVE":
            case 101:
                message.packetType = 101;
                break;
            case "CHAT_MESSAGE":
            case 102:
                message.packetType = 102;
                break;
            case "RED_PACKET_UPDATE":
            case 201:
                message.packetType = 201;
                break;
            case "RED_PACKET_REWARD":
            case 202:
                message.packetType = 202;
                break;
            }
            if (object.data != null)
                if (typeof object.data === "string")
                    $util.base64.decode(object.data, message.data = $util.newBuffer($util.base64.length(object.data)), 0);
                else if (object.data.length >= 0)
                    message.data = object.data;
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a GameMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.GameMessage
         * @static
         * @param {game.GameMessage} message GameMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        GameMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.packetType = options.enums === String ? "PLAYER_NONE" : 0;
                if (options.bytes === String)
                    object.data = "";
                else {
                    object.data = [];
                    if (options.bytes !== Array)
                        object.data = $util.newBuffer(object.data);
                }
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
            }
            if (message.packetType != null && message.hasOwnProperty("packetType"))
                object.packetType = options.enums === String ? $root.game.PacketType[message.packetType] === undefined ? message.packetType : $root.game.PacketType[message.packetType] : message.packetType;
            if (message.data != null && message.hasOwnProperty("data"))
                object.data = options.bytes === String ? $util.base64.encode(message.data, 0, message.data.length) : options.bytes === Array ? Array.prototype.slice.call(message.data) : message.data;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            return object;
        };

        /**
         * Converts this GameMessage to JSON.
         * @function toJSON
         * @memberof game.GameMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        GameMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for GameMessage
         * @function getTypeUrl
         * @memberof game.GameMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        GameMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.GameMessage";
        };

        return GameMessage;
    })();

    game.ClientMessage = (function() {

        /**
         * Properties of a ClientMessage.
         * @memberof game
         * @interface IClientMessage
         * @property {game.PacketType|null} [packetType] ClientMessage packetType
         * @property {Uint8Array|null} [data] ClientMessage data
         * @property {number|Long|null} [timestamp] ClientMessage timestamp
         * @property {string|null} [btcAddress] ClientMessage btcAddress
         */

        /**
         * Constructs a new ClientMessage.
         * @memberof game
         * @classdesc Represents a ClientMessage.
         * @implements IClientMessage
         * @constructor
         * @param {game.IClientMessage=} [properties] Properties to set
         */
        function ClientMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ClientMessage packetType.
         * @member {game.PacketType} packetType
         * @memberof game.ClientMessage
         * @instance
         */
        ClientMessage.prototype.packetType = 0;

        /**
         * ClientMessage data.
         * @member {Uint8Array} data
         * @memberof game.ClientMessage
         * @instance
         */
        ClientMessage.prototype.data = $util.newBuffer([]);

        /**
         * ClientMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof game.ClientMessage
         * @instance
         */
        ClientMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * ClientMessage btcAddress.
         * @member {string} btcAddress
         * @memberof game.ClientMessage
         * @instance
         */
        ClientMessage.prototype.btcAddress = "";

        /**
         * Creates a new ClientMessage instance using the specified properties.
         * @function create
         * @memberof game.ClientMessage
         * @static
         * @param {game.IClientMessage=} [properties] Properties to set
         * @returns {game.ClientMessage} ClientMessage instance
         */
        ClientMessage.create = function create(properties) {
            return new ClientMessage(properties);
        };

        /**
         * Encodes the specified ClientMessage message. Does not implicitly {@link game.ClientMessage.verify|verify} messages.
         * @function encode
         * @memberof game.ClientMessage
         * @static
         * @param {game.IClientMessage} message ClientMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.packetType != null && Object.hasOwnProperty.call(message, "packetType"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.packetType);
            if (message.data != null && Object.hasOwnProperty.call(message, "data"))
                writer.uint32(/* id 2, wireType 2 =*/18).bytes(message.data);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 3, wireType 0 =*/24).int64(message.timestamp);
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.btcAddress);
            return writer;
        };

        /**
         * Encodes the specified ClientMessage message, length delimited. Does not implicitly {@link game.ClientMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ClientMessage
         * @static
         * @param {game.IClientMessage} message ClientMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ClientMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.ClientMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ClientMessage} ClientMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ClientMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.packetType = reader.int32();
                        break;
                    }
                case 2: {
                        message.data = reader.bytes();
                        break;
                    }
                case 3: {
                        message.timestamp = reader.int64();
                        break;
                    }
                case 4: {
                        message.btcAddress = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ClientMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ClientMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ClientMessage} ClientMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ClientMessage message.
         * @function verify
         * @memberof game.ClientMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ClientMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.packetType != null && message.hasOwnProperty("packetType"))
                switch (message.packetType) {
                default:
                    return "packetType: enum value expected";
                case 0:
                case 1:
                case 2:
                case 10:
                case 11:
                case 12:
                case 50:
                case 51:
                case 100:
                case 101:
                case 102:
                case 201:
                case 202:
                    break;
                }
            if (message.data != null && message.hasOwnProperty("data"))
                if (!(message.data && typeof message.data.length === "number" || $util.isString(message.data)))
                    return "data: buffer expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            return null;
        };

        /**
         * Creates a ClientMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ClientMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ClientMessage} ClientMessage
         */
        ClientMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ClientMessage)
                return object;
            var message = new $root.game.ClientMessage();
            switch (object.packetType) {
            default:
                if (typeof object.packetType === "number") {
                    message.packetType = object.packetType;
                    break;
                }
                break;
            case "PLAYER_NONE":
            case 0:
                message.packetType = 0;
                break;
            case "PLAYER_ENTER":
            case 1:
                message.packetType = 1;
                break;
            case "PLAYER_LEAVE":
            case 2:
                message.packetType = 2;
                break;
            case "PLAYER_POSITION":
            case 10:
                message.packetType = 10;
                break;
            case "PLAYER_ANIMATION":
            case 11:
                message.packetType = 11;
                break;
            case "PLAYER_UPDATE":
            case 12:
                message.packetType = 12;
                break;
            case "PET_POSITION":
            case 50:
                message.packetType = 50;
                break;
            case "PET_ANIMATION":
            case 51:
                message.packetType = 51;
                break;
            case "CHAT_ENTER":
            case 100:
                message.packetType = 100;
                break;
            case "CHAT_LEAVE":
            case 101:
                message.packetType = 101;
                break;
            case "CHAT_MESSAGE":
            case 102:
                message.packetType = 102;
                break;
            case "RED_PACKET_UPDATE":
            case 201:
                message.packetType = 201;
                break;
            case "RED_PACKET_REWARD":
            case 202:
                message.packetType = 202;
                break;
            }
            if (object.data != null)
                if (typeof object.data === "string")
                    $util.base64.decode(object.data, message.data = $util.newBuffer($util.base64.length(object.data)), 0);
                else if (object.data.length >= 0)
                    message.data = object.data;
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            return message;
        };

        /**
         * Creates a plain object from a ClientMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ClientMessage
         * @static
         * @param {game.ClientMessage} message ClientMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ClientMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.packetType = options.enums === String ? "PLAYER_NONE" : 0;
                if (options.bytes === String)
                    object.data = "";
                else {
                    object.data = [];
                    if (options.bytes !== Array)
                        object.data = $util.newBuffer(object.data);
                }
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
                object.btcAddress = "";
            }
            if (message.packetType != null && message.hasOwnProperty("packetType"))
                object.packetType = options.enums === String ? $root.game.PacketType[message.packetType] === undefined ? message.packetType : $root.game.PacketType[message.packetType] : message.packetType;
            if (message.data != null && message.hasOwnProperty("data"))
                object.data = options.bytes === String ? $util.base64.encode(message.data, 0, message.data.length) : options.bytes === Array ? Array.prototype.slice.call(message.data) : message.data;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            return object;
        };

        /**
         * Converts this ClientMessage to JSON.
         * @function toJSON
         * @memberof game.ClientMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ClientMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ClientMessage
         * @function getTypeUrl
         * @memberof game.ClientMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ClientMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ClientMessage";
        };

        return ClientMessage;
    })();

    game.PlayerPosition = (function() {

        /**
         * Properties of a PlayerPosition.
         * @memberof game
         * @interface IPlayerPosition
         * @property {string|null} [btcAddress] PlayerPosition btcAddress
         * @property {number|null} [x] PlayerPosition x
         * @property {number|null} [y] PlayerPosition y
         * @property {number|null} [z] PlayerPosition z
         * @property {number|null} [rotationX] PlayerPosition rotationX
         * @property {number|null} [rotationY] PlayerPosition rotationY
         * @property {number|null} [rotationZ] PlayerPosition rotationZ
         * @property {number|null} [rotationW] PlayerPosition rotationW
         */

        /**
         * Constructs a new PlayerPosition.
         * @memberof game
         * @classdesc Represents a PlayerPosition.
         * @implements IPlayerPosition
         * @constructor
         * @param {game.IPlayerPosition=} [properties] Properties to set
         */
        function PlayerPosition(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerPosition btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.btcAddress = "";

        /**
         * PlayerPosition x.
         * @member {number} x
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.x = 0;

        /**
         * PlayerPosition y.
         * @member {number} y
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.y = 0;

        /**
         * PlayerPosition z.
         * @member {number} z
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.z = 0;

        /**
         * PlayerPosition rotationX.
         * @member {number} rotationX
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.rotationX = 0;

        /**
         * PlayerPosition rotationY.
         * @member {number} rotationY
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.rotationY = 0;

        /**
         * PlayerPosition rotationZ.
         * @member {number} rotationZ
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.rotationZ = 0;

        /**
         * PlayerPosition rotationW.
         * @member {number} rotationW
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.rotationW = 0;

        /**
         * Creates a new PlayerPosition instance using the specified properties.
         * @function create
         * @memberof game.PlayerPosition
         * @static
         * @param {game.IPlayerPosition=} [properties] Properties to set
         * @returns {game.PlayerPosition} PlayerPosition instance
         */
        PlayerPosition.create = function create(properties) {
            return new PlayerPosition(properties);
        };

        /**
         * Encodes the specified PlayerPosition message. Does not implicitly {@link game.PlayerPosition.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerPosition
         * @static
         * @param {game.IPlayerPosition} message PlayerPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerPosition.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            if (message.x != null && Object.hasOwnProperty.call(message, "x"))
                writer.uint32(/* id 2, wireType 5 =*/21).float(message.x);
            if (message.y != null && Object.hasOwnProperty.call(message, "y"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.y);
            if (message.z != null && Object.hasOwnProperty.call(message, "z"))
                writer.uint32(/* id 4, wireType 5 =*/37).float(message.z);
            if (message.rotationX != null && Object.hasOwnProperty.call(message, "rotationX"))
                writer.uint32(/* id 5, wireType 5 =*/45).float(message.rotationX);
            if (message.rotationY != null && Object.hasOwnProperty.call(message, "rotationY"))
                writer.uint32(/* id 6, wireType 5 =*/53).float(message.rotationY);
            if (message.rotationZ != null && Object.hasOwnProperty.call(message, "rotationZ"))
                writer.uint32(/* id 7, wireType 5 =*/61).float(message.rotationZ);
            if (message.rotationW != null && Object.hasOwnProperty.call(message, "rotationW"))
                writer.uint32(/* id 8, wireType 5 =*/69).float(message.rotationW);
            return writer;
        };

        /**
         * Encodes the specified PlayerPosition message, length delimited. Does not implicitly {@link game.PlayerPosition.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerPosition
         * @static
         * @param {game.IPlayerPosition} message PlayerPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerPosition.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerPosition message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerPosition} PlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerPosition.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerPosition();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.x = reader.float();
                        break;
                    }
                case 3: {
                        message.y = reader.float();
                        break;
                    }
                case 4: {
                        message.z = reader.float();
                        break;
                    }
                case 5: {
                        message.rotationX = reader.float();
                        break;
                    }
                case 6: {
                        message.rotationY = reader.float();
                        break;
                    }
                case 7: {
                        message.rotationZ = reader.float();
                        break;
                    }
                case 8: {
                        message.rotationW = reader.float();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerPosition message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerPosition} PlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerPosition.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerPosition message.
         * @function verify
         * @memberof game.PlayerPosition
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerPosition.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.x != null && message.hasOwnProperty("x"))
                if (typeof message.x !== "number")
                    return "x: number expected";
            if (message.y != null && message.hasOwnProperty("y"))
                if (typeof message.y !== "number")
                    return "y: number expected";
            if (message.z != null && message.hasOwnProperty("z"))
                if (typeof message.z !== "number")
                    return "z: number expected";
            if (message.rotationX != null && message.hasOwnProperty("rotationX"))
                if (typeof message.rotationX !== "number")
                    return "rotationX: number expected";
            if (message.rotationY != null && message.hasOwnProperty("rotationY"))
                if (typeof message.rotationY !== "number")
                    return "rotationY: number expected";
            if (message.rotationZ != null && message.hasOwnProperty("rotationZ"))
                if (typeof message.rotationZ !== "number")
                    return "rotationZ: number expected";
            if (message.rotationW != null && message.hasOwnProperty("rotationW"))
                if (typeof message.rotationW !== "number")
                    return "rotationW: number expected";
            return null;
        };

        /**
         * Creates a PlayerPosition message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerPosition
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerPosition} PlayerPosition
         */
        PlayerPosition.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerPosition)
                return object;
            var message = new $root.game.PlayerPosition();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.x != null)
                message.x = Number(object.x);
            if (object.y != null)
                message.y = Number(object.y);
            if (object.z != null)
                message.z = Number(object.z);
            if (object.rotationX != null)
                message.rotationX = Number(object.rotationX);
            if (object.rotationY != null)
                message.rotationY = Number(object.rotationY);
            if (object.rotationZ != null)
                message.rotationZ = Number(object.rotationZ);
            if (object.rotationW != null)
                message.rotationW = Number(object.rotationW);
            return message;
        };

        /**
         * Creates a plain object from a PlayerPosition message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerPosition
         * @static
         * @param {game.PlayerPosition} message PlayerPosition
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerPosition.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.btcAddress = "";
                object.x = 0;
                object.y = 0;
                object.z = 0;
                object.rotationX = 0;
                object.rotationY = 0;
                object.rotationZ = 0;
                object.rotationW = 0;
            }
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.x != null && message.hasOwnProperty("x"))
                object.x = options.json && !isFinite(message.x) ? String(message.x) : message.x;
            if (message.y != null && message.hasOwnProperty("y"))
                object.y = options.json && !isFinite(message.y) ? String(message.y) : message.y;
            if (message.z != null && message.hasOwnProperty("z"))
                object.z = options.json && !isFinite(message.z) ? String(message.z) : message.z;
            if (message.rotationX != null && message.hasOwnProperty("rotationX"))
                object.rotationX = options.json && !isFinite(message.rotationX) ? String(message.rotationX) : message.rotationX;
            if (message.rotationY != null && message.hasOwnProperty("rotationY"))
                object.rotationY = options.json && !isFinite(message.rotationY) ? String(message.rotationY) : message.rotationY;
            if (message.rotationZ != null && message.hasOwnProperty("rotationZ"))
                object.rotationZ = options.json && !isFinite(message.rotationZ) ? String(message.rotationZ) : message.rotationZ;
            if (message.rotationW != null && message.hasOwnProperty("rotationW"))
                object.rotationW = options.json && !isFinite(message.rotationW) ? String(message.rotationW) : message.rotationW;
            return object;
        };

        /**
         * Converts this PlayerPosition to JSON.
         * @function toJSON
         * @memberof game.PlayerPosition
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerPosition.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerPosition
         * @function getTypeUrl
         * @memberof game.PlayerPosition
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerPosition.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerPosition";
        };

        return PlayerPosition;
    })();

    game.PlayerAnimation = (function() {

        /**
         * Properties of a PlayerAnimation.
         * @memberof game
         * @interface IPlayerAnimation
         * @property {string|null} [btcAddress] PlayerAnimation btcAddress
         * @property {string|null} [animationName] PlayerAnimation animationName
         * @property {number|null} [speed] PlayerAnimation speed
         * @property {boolean|null} [loop] PlayerAnimation loop
         */

        /**
         * Constructs a new PlayerAnimation.
         * @memberof game
         * @classdesc Represents a PlayerAnimation.
         * @implements IPlayerAnimation
         * @constructor
         * @param {game.IPlayerAnimation=} [properties] Properties to set
         */
        function PlayerAnimation(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerAnimation btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerAnimation
         * @instance
         */
        PlayerAnimation.prototype.btcAddress = "";

        /**
         * PlayerAnimation animationName.
         * @member {string} animationName
         * @memberof game.PlayerAnimation
         * @instance
         */
        PlayerAnimation.prototype.animationName = "";

        /**
         * PlayerAnimation speed.
         * @member {number} speed
         * @memberof game.PlayerAnimation
         * @instance
         */
        PlayerAnimation.prototype.speed = 0;

        /**
         * PlayerAnimation loop.
         * @member {boolean} loop
         * @memberof game.PlayerAnimation
         * @instance
         */
        PlayerAnimation.prototype.loop = false;

        /**
         * Creates a new PlayerAnimation instance using the specified properties.
         * @function create
         * @memberof game.PlayerAnimation
         * @static
         * @param {game.IPlayerAnimation=} [properties] Properties to set
         * @returns {game.PlayerAnimation} PlayerAnimation instance
         */
        PlayerAnimation.create = function create(properties) {
            return new PlayerAnimation(properties);
        };

        /**
         * Encodes the specified PlayerAnimation message. Does not implicitly {@link game.PlayerAnimation.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerAnimation
         * @static
         * @param {game.IPlayerAnimation} message PlayerAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerAnimation.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            if (message.animationName != null && Object.hasOwnProperty.call(message, "animationName"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.animationName);
            if (message.speed != null && Object.hasOwnProperty.call(message, "speed"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.speed);
            if (message.loop != null && Object.hasOwnProperty.call(message, "loop"))
                writer.uint32(/* id 4, wireType 0 =*/32).bool(message.loop);
            return writer;
        };

        /**
         * Encodes the specified PlayerAnimation message, length delimited. Does not implicitly {@link game.PlayerAnimation.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerAnimation
         * @static
         * @param {game.IPlayerAnimation} message PlayerAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerAnimation.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerAnimation message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerAnimation} PlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerAnimation.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerAnimation();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.animationName = reader.string();
                        break;
                    }
                case 3: {
                        message.speed = reader.float();
                        break;
                    }
                case 4: {
                        message.loop = reader.bool();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerAnimation message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerAnimation} PlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerAnimation.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerAnimation message.
         * @function verify
         * @memberof game.PlayerAnimation
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerAnimation.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.animationName != null && message.hasOwnProperty("animationName"))
                if (!$util.isString(message.animationName))
                    return "animationName: string expected";
            if (message.speed != null && message.hasOwnProperty("speed"))
                if (typeof message.speed !== "number")
                    return "speed: number expected";
            if (message.loop != null && message.hasOwnProperty("loop"))
                if (typeof message.loop !== "boolean")
                    return "loop: boolean expected";
            return null;
        };

        /**
         * Creates a PlayerAnimation message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerAnimation
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerAnimation} PlayerAnimation
         */
        PlayerAnimation.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerAnimation)
                return object;
            var message = new $root.game.PlayerAnimation();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.animationName != null)
                message.animationName = String(object.animationName);
            if (object.speed != null)
                message.speed = Number(object.speed);
            if (object.loop != null)
                message.loop = Boolean(object.loop);
            return message;
        };

        /**
         * Creates a plain object from a PlayerAnimation message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerAnimation
         * @static
         * @param {game.PlayerAnimation} message PlayerAnimation
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerAnimation.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.btcAddress = "";
                object.animationName = "";
                object.speed = 0;
                object.loop = false;
            }
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.animationName != null && message.hasOwnProperty("animationName"))
                object.animationName = message.animationName;
            if (message.speed != null && message.hasOwnProperty("speed"))
                object.speed = options.json && !isFinite(message.speed) ? String(message.speed) : message.speed;
            if (message.loop != null && message.hasOwnProperty("loop"))
                object.loop = message.loop;
            return object;
        };

        /**
         * Converts this PlayerAnimation to JSON.
         * @function toJSON
         * @memberof game.PlayerAnimation
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerAnimation.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerAnimation
         * @function getTypeUrl
         * @memberof game.PlayerAnimation
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerAnimation.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerAnimation";
        };

        return PlayerAnimation;
    })();

    game.PlayerUpdate = (function() {

        /**
         * Properties of a PlayerUpdate.
         * @memberof game
         * @interface IPlayerUpdate
         * @property {string|null} [btcAddress] PlayerUpdate btcAddress
         * @property {string|null} [key] PlayerUpdate key
         * @property {string|null} [value] PlayerUpdate value
         */

        /**
         * Constructs a new PlayerUpdate.
         * @memberof game
         * @classdesc Represents a PlayerUpdate.
         * @implements IPlayerUpdate
         * @constructor
         * @param {game.IPlayerUpdate=} [properties] Properties to set
         */
        function PlayerUpdate(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerUpdate btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerUpdate
         * @instance
         */
        PlayerUpdate.prototype.btcAddress = "";

        /**
         * PlayerUpdate key.
         * @member {string} key
         * @memberof game.PlayerUpdate
         * @instance
         */
        PlayerUpdate.prototype.key = "";

        /**
         * PlayerUpdate value.
         * @member {string} value
         * @memberof game.PlayerUpdate
         * @instance
         */
        PlayerUpdate.prototype.value = "";

        /**
         * Creates a new PlayerUpdate instance using the specified properties.
         * @function create
         * @memberof game.PlayerUpdate
         * @static
         * @param {game.IPlayerUpdate=} [properties] Properties to set
         * @returns {game.PlayerUpdate} PlayerUpdate instance
         */
        PlayerUpdate.create = function create(properties) {
            return new PlayerUpdate(properties);
        };

        /**
         * Encodes the specified PlayerUpdate message. Does not implicitly {@link game.PlayerUpdate.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerUpdate
         * @static
         * @param {game.IPlayerUpdate} message PlayerUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerUpdate.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            if (message.key != null && Object.hasOwnProperty.call(message, "key"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.key);
            if (message.value != null && Object.hasOwnProperty.call(message, "value"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.value);
            return writer;
        };

        /**
         * Encodes the specified PlayerUpdate message, length delimited. Does not implicitly {@link game.PlayerUpdate.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerUpdate
         * @static
         * @param {game.IPlayerUpdate} message PlayerUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerUpdate.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerUpdate message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerUpdate} PlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerUpdate.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerUpdate();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.key = reader.string();
                        break;
                    }
                case 3: {
                        message.value = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerUpdate message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerUpdate} PlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerUpdate.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerUpdate message.
         * @function verify
         * @memberof game.PlayerUpdate
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerUpdate.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.key != null && message.hasOwnProperty("key"))
                if (!$util.isString(message.key))
                    return "key: string expected";
            if (message.value != null && message.hasOwnProperty("value"))
                if (!$util.isString(message.value))
                    return "value: string expected";
            return null;
        };

        /**
         * Creates a PlayerUpdate message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerUpdate
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerUpdate} PlayerUpdate
         */
        PlayerUpdate.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerUpdate)
                return object;
            var message = new $root.game.PlayerUpdate();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.key != null)
                message.key = String(object.key);
            if (object.value != null)
                message.value = String(object.value);
            return message;
        };

        /**
         * Creates a plain object from a PlayerUpdate message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerUpdate
         * @static
         * @param {game.PlayerUpdate} message PlayerUpdate
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerUpdate.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.btcAddress = "";
                object.key = "";
                object.value = "";
            }
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.key != null && message.hasOwnProperty("key"))
                object.key = message.key;
            if (message.value != null && message.hasOwnProperty("value"))
                object.value = message.value;
            return object;
        };

        /**
         * Converts this PlayerUpdate to JSON.
         * @function toJSON
         * @memberof game.PlayerUpdate
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerUpdate.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerUpdate
         * @function getTypeUrl
         * @memberof game.PlayerUpdate
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerUpdate.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerUpdate";
        };

        return PlayerUpdate;
    })();

    game.PlayerEnter = (function() {

        /**
         * Properties of a PlayerEnter.
         * @memberof game
         * @interface IPlayerEnter
         * @property {string|null} [btcAddress] PlayerEnter btcAddress
         * @property {string|null} [sessionId] PlayerEnter sessionId
         * @property {game.IAvatarData|null} [avatarData] PlayerEnter avatarData
         * @property {game.IPlayerPosition|null} [position] PlayerEnter position
         */

        /**
         * Constructs a new PlayerEnter.
         * @memberof game
         * @classdesc Represents a PlayerEnter.
         * @implements IPlayerEnter
         * @constructor
         * @param {game.IPlayerEnter=} [properties] Properties to set
         */
        function PlayerEnter(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerEnter btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerEnter
         * @instance
         */
        PlayerEnter.prototype.btcAddress = "";

        /**
         * PlayerEnter sessionId.
         * @member {string} sessionId
         * @memberof game.PlayerEnter
         * @instance
         */
        PlayerEnter.prototype.sessionId = "";

        /**
         * PlayerEnter avatarData.
         * @member {game.IAvatarData|null|undefined} avatarData
         * @memberof game.PlayerEnter
         * @instance
         */
        PlayerEnter.prototype.avatarData = null;

        /**
         * PlayerEnter position.
         * @member {game.IPlayerPosition|null|undefined} position
         * @memberof game.PlayerEnter
         * @instance
         */
        PlayerEnter.prototype.position = null;

        /**
         * Creates a new PlayerEnter instance using the specified properties.
         * @function create
         * @memberof game.PlayerEnter
         * @static
         * @param {game.IPlayerEnter=} [properties] Properties to set
         * @returns {game.PlayerEnter} PlayerEnter instance
         */
        PlayerEnter.create = function create(properties) {
            return new PlayerEnter(properties);
        };

        /**
         * Encodes the specified PlayerEnter message. Does not implicitly {@link game.PlayerEnter.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerEnter
         * @static
         * @param {game.IPlayerEnter} message PlayerEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerEnter.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            if (message.sessionId != null && Object.hasOwnProperty.call(message, "sessionId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.sessionId);
            if (message.avatarData != null && Object.hasOwnProperty.call(message, "avatarData"))
                $root.game.AvatarData.encode(message.avatarData, writer.uint32(/* id 3, wireType 2 =*/26).fork()).ldelim();
            if (message.position != null && Object.hasOwnProperty.call(message, "position"))
                $root.game.PlayerPosition.encode(message.position, writer.uint32(/* id 4, wireType 2 =*/34).fork()).ldelim();
            return writer;
        };

        /**
         * Encodes the specified PlayerEnter message, length delimited. Does not implicitly {@link game.PlayerEnter.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerEnter
         * @static
         * @param {game.IPlayerEnter} message PlayerEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerEnter.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerEnter message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerEnter} PlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerEnter.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerEnter();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.sessionId = reader.string();
                        break;
                    }
                case 3: {
                        message.avatarData = $root.game.AvatarData.decode(reader, reader.uint32());
                        break;
                    }
                case 4: {
                        message.position = $root.game.PlayerPosition.decode(reader, reader.uint32());
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerEnter message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerEnter} PlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerEnter.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerEnter message.
         * @function verify
         * @memberof game.PlayerEnter
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerEnter.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.sessionId != null && message.hasOwnProperty("sessionId"))
                if (!$util.isString(message.sessionId))
                    return "sessionId: string expected";
            if (message.avatarData != null && message.hasOwnProperty("avatarData")) {
                var error = $root.game.AvatarData.verify(message.avatarData);
                if (error)
                    return "avatarData." + error;
            }
            if (message.position != null && message.hasOwnProperty("position")) {
                var error = $root.game.PlayerPosition.verify(message.position);
                if (error)
                    return "position." + error;
            }
            return null;
        };

        /**
         * Creates a PlayerEnter message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerEnter
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerEnter} PlayerEnter
         */
        PlayerEnter.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerEnter)
                return object;
            var message = new $root.game.PlayerEnter();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.sessionId != null)
                message.sessionId = String(object.sessionId);
            if (object.avatarData != null) {
                if (typeof object.avatarData !== "object")
                    throw TypeError(".game.PlayerEnter.avatarData: object expected");
                message.avatarData = $root.game.AvatarData.fromObject(object.avatarData);
            }
            if (object.position != null) {
                if (typeof object.position !== "object")
                    throw TypeError(".game.PlayerEnter.position: object expected");
                message.position = $root.game.PlayerPosition.fromObject(object.position);
            }
            return message;
        };

        /**
         * Creates a plain object from a PlayerEnter message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerEnter
         * @static
         * @param {game.PlayerEnter} message PlayerEnter
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerEnter.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.btcAddress = "";
                object.sessionId = "";
                object.avatarData = null;
                object.position = null;
            }
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.sessionId != null && message.hasOwnProperty("sessionId"))
                object.sessionId = message.sessionId;
            if (message.avatarData != null && message.hasOwnProperty("avatarData"))
                object.avatarData = $root.game.AvatarData.toObject(message.avatarData, options);
            if (message.position != null && message.hasOwnProperty("position"))
                object.position = $root.game.PlayerPosition.toObject(message.position, options);
            return object;
        };

        /**
         * Converts this PlayerEnter to JSON.
         * @function toJSON
         * @memberof game.PlayerEnter
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerEnter.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerEnter
         * @function getTypeUrl
         * @memberof game.PlayerEnter
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerEnter.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerEnter";
        };

        return PlayerEnter;
    })();

    game.PlayerLeave = (function() {

        /**
         * Properties of a PlayerLeave.
         * @memberof game
         * @interface IPlayerLeave
         * @property {string|null} [btcAddress] PlayerLeave btcAddress
         */

        /**
         * Constructs a new PlayerLeave.
         * @memberof game
         * @classdesc Represents a PlayerLeave.
         * @implements IPlayerLeave
         * @constructor
         * @param {game.IPlayerLeave=} [properties] Properties to set
         */
        function PlayerLeave(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerLeave btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerLeave
         * @instance
         */
        PlayerLeave.prototype.btcAddress = "";

        /**
         * Creates a new PlayerLeave instance using the specified properties.
         * @function create
         * @memberof game.PlayerLeave
         * @static
         * @param {game.IPlayerLeave=} [properties] Properties to set
         * @returns {game.PlayerLeave} PlayerLeave instance
         */
        PlayerLeave.create = function create(properties) {
            return new PlayerLeave(properties);
        };

        /**
         * Encodes the specified PlayerLeave message. Does not implicitly {@link game.PlayerLeave.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerLeave
         * @static
         * @param {game.IPlayerLeave} message PlayerLeave message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerLeave.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            return writer;
        };

        /**
         * Encodes the specified PlayerLeave message, length delimited. Does not implicitly {@link game.PlayerLeave.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerLeave
         * @static
         * @param {game.IPlayerLeave} message PlayerLeave message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerLeave.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerLeave message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerLeave
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerLeave} PlayerLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerLeave.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerLeave();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerLeave message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerLeave
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerLeave} PlayerLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerLeave.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerLeave message.
         * @function verify
         * @memberof game.PlayerLeave
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerLeave.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            return null;
        };

        /**
         * Creates a PlayerLeave message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerLeave
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerLeave} PlayerLeave
         */
        PlayerLeave.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerLeave)
                return object;
            var message = new $root.game.PlayerLeave();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            return message;
        };

        /**
         * Creates a plain object from a PlayerLeave message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerLeave
         * @static
         * @param {game.PlayerLeave} message PlayerLeave
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerLeave.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.btcAddress = "";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            return object;
        };

        /**
         * Converts this PlayerLeave to JSON.
         * @function toJSON
         * @memberof game.PlayerLeave
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerLeave.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerLeave
         * @function getTypeUrl
         * @memberof game.PlayerLeave
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerLeave.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerLeave";
        };

        return PlayerLeave;
    })();

    game.AvatarData = (function() {

        /**
         * Properties of an AvatarData.
         * @memberof game
         * @interface IAvatarData
         * @property {string|null} [shirtId] AvatarData shirtId
         * @property {string|null} [shirtTextureId] AvatarData shirtTextureId
         * @property {string|null} [shirtColor] AvatarData shirtColor
         * @property {string|null} [pantsId] AvatarData pantsId
         * @property {string|null} [shoesId] AvatarData shoesId
         * @property {string|null} [hatId] AvatarData hatId
         */

        /**
         * Constructs a new AvatarData.
         * @memberof game
         * @classdesc Represents an AvatarData.
         * @implements IAvatarData
         * @constructor
         * @param {game.IAvatarData=} [properties] Properties to set
         */
        function AvatarData(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * AvatarData shirtId.
         * @member {string} shirtId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.shirtId = "";

        /**
         * AvatarData shirtTextureId.
         * @member {string} shirtTextureId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.shirtTextureId = "";

        /**
         * AvatarData shirtColor.
         * @member {string} shirtColor
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.shirtColor = "";

        /**
         * AvatarData pantsId.
         * @member {string} pantsId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.pantsId = "";

        /**
         * AvatarData shoesId.
         * @member {string} shoesId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.shoesId = "";

        /**
         * AvatarData hatId.
         * @member {string} hatId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.hatId = "";

        /**
         * Creates a new AvatarData instance using the specified properties.
         * @function create
         * @memberof game.AvatarData
         * @static
         * @param {game.IAvatarData=} [properties] Properties to set
         * @returns {game.AvatarData} AvatarData instance
         */
        AvatarData.create = function create(properties) {
            return new AvatarData(properties);
        };

        /**
         * Encodes the specified AvatarData message. Does not implicitly {@link game.AvatarData.verify|verify} messages.
         * @function encode
         * @memberof game.AvatarData
         * @static
         * @param {game.IAvatarData} message AvatarData message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        AvatarData.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.shirtId != null && Object.hasOwnProperty.call(message, "shirtId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.shirtId);
            if (message.shirtTextureId != null && Object.hasOwnProperty.call(message, "shirtTextureId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.shirtTextureId);
            if (message.shirtColor != null && Object.hasOwnProperty.call(message, "shirtColor"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.shirtColor);
            if (message.pantsId != null && Object.hasOwnProperty.call(message, "pantsId"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.pantsId);
            if (message.shoesId != null && Object.hasOwnProperty.call(message, "shoesId"))
                writer.uint32(/* id 5, wireType 2 =*/42).string(message.shoesId);
            if (message.hatId != null && Object.hasOwnProperty.call(message, "hatId"))
                writer.uint32(/* id 6, wireType 2 =*/50).string(message.hatId);
            return writer;
        };

        /**
         * Encodes the specified AvatarData message, length delimited. Does not implicitly {@link game.AvatarData.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.AvatarData
         * @static
         * @param {game.IAvatarData} message AvatarData message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        AvatarData.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an AvatarData message from the specified reader or buffer.
         * @function decode
         * @memberof game.AvatarData
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.AvatarData} AvatarData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        AvatarData.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.AvatarData();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.shirtId = reader.string();
                        break;
                    }
                case 2: {
                        message.shirtTextureId = reader.string();
                        break;
                    }
                case 3: {
                        message.shirtColor = reader.string();
                        break;
                    }
                case 4: {
                        message.pantsId = reader.string();
                        break;
                    }
                case 5: {
                        message.shoesId = reader.string();
                        break;
                    }
                case 6: {
                        message.hatId = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an AvatarData message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.AvatarData
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.AvatarData} AvatarData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        AvatarData.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an AvatarData message.
         * @function verify
         * @memberof game.AvatarData
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        AvatarData.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.shirtId != null && message.hasOwnProperty("shirtId"))
                if (!$util.isString(message.shirtId))
                    return "shirtId: string expected";
            if (message.shirtTextureId != null && message.hasOwnProperty("shirtTextureId"))
                if (!$util.isString(message.shirtTextureId))
                    return "shirtTextureId: string expected";
            if (message.shirtColor != null && message.hasOwnProperty("shirtColor"))
                if (!$util.isString(message.shirtColor))
                    return "shirtColor: string expected";
            if (message.pantsId != null && message.hasOwnProperty("pantsId"))
                if (!$util.isString(message.pantsId))
                    return "pantsId: string expected";
            if (message.shoesId != null && message.hasOwnProperty("shoesId"))
                if (!$util.isString(message.shoesId))
                    return "shoesId: string expected";
            if (message.hatId != null && message.hasOwnProperty("hatId"))
                if (!$util.isString(message.hatId))
                    return "hatId: string expected";
            return null;
        };

        /**
         * Creates an AvatarData message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.AvatarData
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.AvatarData} AvatarData
         */
        AvatarData.fromObject = function fromObject(object) {
            if (object instanceof $root.game.AvatarData)
                return object;
            var message = new $root.game.AvatarData();
            if (object.shirtId != null)
                message.shirtId = String(object.shirtId);
            if (object.shirtTextureId != null)
                message.shirtTextureId = String(object.shirtTextureId);
            if (object.shirtColor != null)
                message.shirtColor = String(object.shirtColor);
            if (object.pantsId != null)
                message.pantsId = String(object.pantsId);
            if (object.shoesId != null)
                message.shoesId = String(object.shoesId);
            if (object.hatId != null)
                message.hatId = String(object.hatId);
            return message;
        };

        /**
         * Creates a plain object from an AvatarData message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.AvatarData
         * @static
         * @param {game.AvatarData} message AvatarData
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        AvatarData.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.shirtId = "";
                object.shirtTextureId = "";
                object.shirtColor = "";
                object.pantsId = "";
                object.shoesId = "";
                object.hatId = "";
            }
            if (message.shirtId != null && message.hasOwnProperty("shirtId"))
                object.shirtId = message.shirtId;
            if (message.shirtTextureId != null && message.hasOwnProperty("shirtTextureId"))
                object.shirtTextureId = message.shirtTextureId;
            if (message.shirtColor != null && message.hasOwnProperty("shirtColor"))
                object.shirtColor = message.shirtColor;
            if (message.pantsId != null && message.hasOwnProperty("pantsId"))
                object.pantsId = message.pantsId;
            if (message.shoesId != null && message.hasOwnProperty("shoesId"))
                object.shoesId = message.shoesId;
            if (message.hatId != null && message.hasOwnProperty("hatId"))
                object.hatId = message.hatId;
            return object;
        };

        /**
         * Converts this AvatarData to JSON.
         * @function toJSON
         * @memberof game.AvatarData
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        AvatarData.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for AvatarData
         * @function getTypeUrl
         * @memberof game.AvatarData
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        AvatarData.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.AvatarData";
        };

        return AvatarData;
    })();

    game.PetPosition = (function() {

        /**
         * Properties of a PetPosition.
         * @memberof game
         * @interface IPetPosition
         * @property {string|null} [ownerBtcAddress] PetPosition ownerBtcAddress
         * @property {string|null} [petId] PetPosition petId
         * @property {number|null} [x] PetPosition x
         * @property {number|null} [y] PetPosition y
         * @property {number|null} [z] PetPosition z
         * @property {number|null} [rotationX] PetPosition rotationX
         * @property {number|null} [rotationY] PetPosition rotationY
         * @property {number|null} [rotationZ] PetPosition rotationZ
         * @property {number|null} [rotationW] PetPosition rotationW
         */

        /**
         * Constructs a new PetPosition.
         * @memberof game
         * @classdesc Represents a PetPosition.
         * @implements IPetPosition
         * @constructor
         * @param {game.IPetPosition=} [properties] Properties to set
         */
        function PetPosition(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PetPosition ownerBtcAddress.
         * @member {string} ownerBtcAddress
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.ownerBtcAddress = "";

        /**
         * PetPosition petId.
         * @member {string} petId
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.petId = "";

        /**
         * PetPosition x.
         * @member {number} x
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.x = 0;

        /**
         * PetPosition y.
         * @member {number} y
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.y = 0;

        /**
         * PetPosition z.
         * @member {number} z
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.z = 0;

        /**
         * PetPosition rotationX.
         * @member {number} rotationX
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.rotationX = 0;

        /**
         * PetPosition rotationY.
         * @member {number} rotationY
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.rotationY = 0;

        /**
         * PetPosition rotationZ.
         * @member {number} rotationZ
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.rotationZ = 0;

        /**
         * PetPosition rotationW.
         * @member {number} rotationW
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.rotationW = 0;

        /**
         * Creates a new PetPosition instance using the specified properties.
         * @function create
         * @memberof game.PetPosition
         * @static
         * @param {game.IPetPosition=} [properties] Properties to set
         * @returns {game.PetPosition} PetPosition instance
         */
        PetPosition.create = function create(properties) {
            return new PetPosition(properties);
        };

        /**
         * Encodes the specified PetPosition message. Does not implicitly {@link game.PetPosition.verify|verify} messages.
         * @function encode
         * @memberof game.PetPosition
         * @static
         * @param {game.IPetPosition} message PetPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PetPosition.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.ownerBtcAddress != null && Object.hasOwnProperty.call(message, "ownerBtcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.ownerBtcAddress);
            if (message.petId != null && Object.hasOwnProperty.call(message, "petId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.petId);
            if (message.x != null && Object.hasOwnProperty.call(message, "x"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.x);
            if (message.y != null && Object.hasOwnProperty.call(message, "y"))
                writer.uint32(/* id 4, wireType 5 =*/37).float(message.y);
            if (message.z != null && Object.hasOwnProperty.call(message, "z"))
                writer.uint32(/* id 5, wireType 5 =*/45).float(message.z);
            if (message.rotationX != null && Object.hasOwnProperty.call(message, "rotationX"))
                writer.uint32(/* id 6, wireType 5 =*/53).float(message.rotationX);
            if (message.rotationY != null && Object.hasOwnProperty.call(message, "rotationY"))
                writer.uint32(/* id 7, wireType 5 =*/61).float(message.rotationY);
            if (message.rotationZ != null && Object.hasOwnProperty.call(message, "rotationZ"))
                writer.uint32(/* id 8, wireType 5 =*/69).float(message.rotationZ);
            if (message.rotationW != null && Object.hasOwnProperty.call(message, "rotationW"))
                writer.uint32(/* id 9, wireType 5 =*/77).float(message.rotationW);
            return writer;
        };

        /**
         * Encodes the specified PetPosition message, length delimited. Does not implicitly {@link game.PetPosition.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PetPosition
         * @static
         * @param {game.IPetPosition} message PetPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PetPosition.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PetPosition message from the specified reader or buffer.
         * @function decode
         * @memberof game.PetPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PetPosition} PetPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PetPosition.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PetPosition();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.ownerBtcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.petId = reader.string();
                        break;
                    }
                case 3: {
                        message.x = reader.float();
                        break;
                    }
                case 4: {
                        message.y = reader.float();
                        break;
                    }
                case 5: {
                        message.z = reader.float();
                        break;
                    }
                case 6: {
                        message.rotationX = reader.float();
                        break;
                    }
                case 7: {
                        message.rotationY = reader.float();
                        break;
                    }
                case 8: {
                        message.rotationZ = reader.float();
                        break;
                    }
                case 9: {
                        message.rotationW = reader.float();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PetPosition message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PetPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PetPosition} PetPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PetPosition.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PetPosition message.
         * @function verify
         * @memberof game.PetPosition
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PetPosition.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.ownerBtcAddress != null && message.hasOwnProperty("ownerBtcAddress"))
                if (!$util.isString(message.ownerBtcAddress))
                    return "ownerBtcAddress: string expected";
            if (message.petId != null && message.hasOwnProperty("petId"))
                if (!$util.isString(message.petId))
                    return "petId: string expected";
            if (message.x != null && message.hasOwnProperty("x"))
                if (typeof message.x !== "number")
                    return "x: number expected";
            if (message.y != null && message.hasOwnProperty("y"))
                if (typeof message.y !== "number")
                    return "y: number expected";
            if (message.z != null && message.hasOwnProperty("z"))
                if (typeof message.z !== "number")
                    return "z: number expected";
            if (message.rotationX != null && message.hasOwnProperty("rotationX"))
                if (typeof message.rotationX !== "number")
                    return "rotationX: number expected";
            if (message.rotationY != null && message.hasOwnProperty("rotationY"))
                if (typeof message.rotationY !== "number")
                    return "rotationY: number expected";
            if (message.rotationZ != null && message.hasOwnProperty("rotationZ"))
                if (typeof message.rotationZ !== "number")
                    return "rotationZ: number expected";
            if (message.rotationW != null && message.hasOwnProperty("rotationW"))
                if (typeof message.rotationW !== "number")
                    return "rotationW: number expected";
            return null;
        };

        /**
         * Creates a PetPosition message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PetPosition
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PetPosition} PetPosition
         */
        PetPosition.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PetPosition)
                return object;
            var message = new $root.game.PetPosition();
            if (object.ownerBtcAddress != null)
                message.ownerBtcAddress = String(object.ownerBtcAddress);
            if (object.petId != null)
                message.petId = String(object.petId);
            if (object.x != null)
                message.x = Number(object.x);
            if (object.y != null)
                message.y = Number(object.y);
            if (object.z != null)
                message.z = Number(object.z);
            if (object.rotationX != null)
                message.rotationX = Number(object.rotationX);
            if (object.rotationY != null)
                message.rotationY = Number(object.rotationY);
            if (object.rotationZ != null)
                message.rotationZ = Number(object.rotationZ);
            if (object.rotationW != null)
                message.rotationW = Number(object.rotationW);
            return message;
        };

        /**
         * Creates a plain object from a PetPosition message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PetPosition
         * @static
         * @param {game.PetPosition} message PetPosition
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PetPosition.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.ownerBtcAddress = "";
                object.petId = "";
                object.x = 0;
                object.y = 0;
                object.z = 0;
                object.rotationX = 0;
                object.rotationY = 0;
                object.rotationZ = 0;
                object.rotationW = 0;
            }
            if (message.ownerBtcAddress != null && message.hasOwnProperty("ownerBtcAddress"))
                object.ownerBtcAddress = message.ownerBtcAddress;
            if (message.petId != null && message.hasOwnProperty("petId"))
                object.petId = message.petId;
            if (message.x != null && message.hasOwnProperty("x"))
                object.x = options.json && !isFinite(message.x) ? String(message.x) : message.x;
            if (message.y != null && message.hasOwnProperty("y"))
                object.y = options.json && !isFinite(message.y) ? String(message.y) : message.y;
            if (message.z != null && message.hasOwnProperty("z"))
                object.z = options.json && !isFinite(message.z) ? String(message.z) : message.z;
            if (message.rotationX != null && message.hasOwnProperty("rotationX"))
                object.rotationX = options.json && !isFinite(message.rotationX) ? String(message.rotationX) : message.rotationX;
            if (message.rotationY != null && message.hasOwnProperty("rotationY"))
                object.rotationY = options.json && !isFinite(message.rotationY) ? String(message.rotationY) : message.rotationY;
            if (message.rotationZ != null && message.hasOwnProperty("rotationZ"))
                object.rotationZ = options.json && !isFinite(message.rotationZ) ? String(message.rotationZ) : message.rotationZ;
            if (message.rotationW != null && message.hasOwnProperty("rotationW"))
                object.rotationW = options.json && !isFinite(message.rotationW) ? String(message.rotationW) : message.rotationW;
            return object;
        };

        /**
         * Converts this PetPosition to JSON.
         * @function toJSON
         * @memberof game.PetPosition
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PetPosition.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PetPosition
         * @function getTypeUrl
         * @memberof game.PetPosition
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PetPosition.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PetPosition";
        };

        return PetPosition;
    })();

    game.PetAnimation = (function() {

        /**
         * Properties of a PetAnimation.
         * @memberof game
         * @interface IPetAnimation
         * @property {string|null} [ownerBtcAddress] PetAnimation ownerBtcAddress
         * @property {string|null} [petId] PetAnimation petId
         * @property {string|null} [animationName] PetAnimation animationName
         */

        /**
         * Constructs a new PetAnimation.
         * @memberof game
         * @classdesc Represents a PetAnimation.
         * @implements IPetAnimation
         * @constructor
         * @param {game.IPetAnimation=} [properties] Properties to set
         */
        function PetAnimation(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PetAnimation ownerBtcAddress.
         * @member {string} ownerBtcAddress
         * @memberof game.PetAnimation
         * @instance
         */
        PetAnimation.prototype.ownerBtcAddress = "";

        /**
         * PetAnimation petId.
         * @member {string} petId
         * @memberof game.PetAnimation
         * @instance
         */
        PetAnimation.prototype.petId = "";

        /**
         * PetAnimation animationName.
         * @member {string} animationName
         * @memberof game.PetAnimation
         * @instance
         */
        PetAnimation.prototype.animationName = "";

        /**
         * Creates a new PetAnimation instance using the specified properties.
         * @function create
         * @memberof game.PetAnimation
         * @static
         * @param {game.IPetAnimation=} [properties] Properties to set
         * @returns {game.PetAnimation} PetAnimation instance
         */
        PetAnimation.create = function create(properties) {
            return new PetAnimation(properties);
        };

        /**
         * Encodes the specified PetAnimation message. Does not implicitly {@link game.PetAnimation.verify|verify} messages.
         * @function encode
         * @memberof game.PetAnimation
         * @static
         * @param {game.IPetAnimation} message PetAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PetAnimation.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.ownerBtcAddress != null && Object.hasOwnProperty.call(message, "ownerBtcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.ownerBtcAddress);
            if (message.petId != null && Object.hasOwnProperty.call(message, "petId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.petId);
            if (message.animationName != null && Object.hasOwnProperty.call(message, "animationName"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.animationName);
            return writer;
        };

        /**
         * Encodes the specified PetAnimation message, length delimited. Does not implicitly {@link game.PetAnimation.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PetAnimation
         * @static
         * @param {game.IPetAnimation} message PetAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PetAnimation.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PetAnimation message from the specified reader or buffer.
         * @function decode
         * @memberof game.PetAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PetAnimation} PetAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PetAnimation.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PetAnimation();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.ownerBtcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.petId = reader.string();
                        break;
                    }
                case 3: {
                        message.animationName = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PetAnimation message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PetAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PetAnimation} PetAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PetAnimation.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PetAnimation message.
         * @function verify
         * @memberof game.PetAnimation
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PetAnimation.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.ownerBtcAddress != null && message.hasOwnProperty("ownerBtcAddress"))
                if (!$util.isString(message.ownerBtcAddress))
                    return "ownerBtcAddress: string expected";
            if (message.petId != null && message.hasOwnProperty("petId"))
                if (!$util.isString(message.petId))
                    return "petId: string expected";
            if (message.animationName != null && message.hasOwnProperty("animationName"))
                if (!$util.isString(message.animationName))
                    return "animationName: string expected";
            return null;
        };

        /**
         * Creates a PetAnimation message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PetAnimation
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PetAnimation} PetAnimation
         */
        PetAnimation.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PetAnimation)
                return object;
            var message = new $root.game.PetAnimation();
            if (object.ownerBtcAddress != null)
                message.ownerBtcAddress = String(object.ownerBtcAddress);
            if (object.petId != null)
                message.petId = String(object.petId);
            if (object.animationName != null)
                message.animationName = String(object.animationName);
            return message;
        };

        /**
         * Creates a plain object from a PetAnimation message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PetAnimation
         * @static
         * @param {game.PetAnimation} message PetAnimation
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PetAnimation.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.ownerBtcAddress = "";
                object.petId = "";
                object.animationName = "";
            }
            if (message.ownerBtcAddress != null && message.hasOwnProperty("ownerBtcAddress"))
                object.ownerBtcAddress = message.ownerBtcAddress;
            if (message.petId != null && message.hasOwnProperty("petId"))
                object.petId = message.petId;
            if (message.animationName != null && message.hasOwnProperty("animationName"))
                object.animationName = message.animationName;
            return object;
        };

        /**
         * Converts this PetAnimation to JSON.
         * @function toJSON
         * @memberof game.PetAnimation
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PetAnimation.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PetAnimation
         * @function getTypeUrl
         * @memberof game.PetAnimation
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PetAnimation.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PetAnimation";
        };

        return PetAnimation;
    })();

    game.ChatEnter = (function() {

        /**
         * Properties of a ChatEnter.
         * @memberof game
         * @interface IChatEnter
         * @property {number|null} [chatId] ChatEnter chatId
         * @property {string|null} [btcAddress] ChatEnter btcAddress
         */

        /**
         * Constructs a new ChatEnter.
         * @memberof game
         * @classdesc Represents a ChatEnter.
         * @implements IChatEnter
         * @constructor
         * @param {game.IChatEnter=} [properties] Properties to set
         */
        function ChatEnter(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ChatEnter chatId.
         * @member {number} chatId
         * @memberof game.ChatEnter
         * @instance
         */
        ChatEnter.prototype.chatId = 0;

        /**
         * ChatEnter btcAddress.
         * @member {string} btcAddress
         * @memberof game.ChatEnter
         * @instance
         */
        ChatEnter.prototype.btcAddress = "";

        /**
         * Creates a new ChatEnter instance using the specified properties.
         * @function create
         * @memberof game.ChatEnter
         * @static
         * @param {game.IChatEnter=} [properties] Properties to set
         * @returns {game.ChatEnter} ChatEnter instance
         */
        ChatEnter.create = function create(properties) {
            return new ChatEnter(properties);
        };

        /**
         * Encodes the specified ChatEnter message. Does not implicitly {@link game.ChatEnter.verify|verify} messages.
         * @function encode
         * @memberof game.ChatEnter
         * @static
         * @param {game.IChatEnter} message ChatEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatEnter.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.chatId != null && Object.hasOwnProperty.call(message, "chatId"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.chatId);
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.btcAddress);
            return writer;
        };

        /**
         * Encodes the specified ChatEnter message, length delimited. Does not implicitly {@link game.ChatEnter.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ChatEnter
         * @static
         * @param {game.IChatEnter} message ChatEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatEnter.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ChatEnter message from the specified reader or buffer.
         * @function decode
         * @memberof game.ChatEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ChatEnter} ChatEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatEnter.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ChatEnter();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.chatId = reader.int32();
                        break;
                    }
                case 2: {
                        message.btcAddress = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ChatEnter message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ChatEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ChatEnter} ChatEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatEnter.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ChatEnter message.
         * @function verify
         * @memberof game.ChatEnter
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ChatEnter.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                if (!$util.isInteger(message.chatId))
                    return "chatId: integer expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            return null;
        };

        /**
         * Creates a ChatEnter message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ChatEnter
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ChatEnter} ChatEnter
         */
        ChatEnter.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ChatEnter)
                return object;
            var message = new $root.game.ChatEnter();
            if (object.chatId != null)
                message.chatId = object.chatId | 0;
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            return message;
        };

        /**
         * Creates a plain object from a ChatEnter message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ChatEnter
         * @static
         * @param {game.ChatEnter} message ChatEnter
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ChatEnter.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.chatId = 0;
                object.btcAddress = "";
            }
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                object.chatId = message.chatId;
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            return object;
        };

        /**
         * Converts this ChatEnter to JSON.
         * @function toJSON
         * @memberof game.ChatEnter
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ChatEnter.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ChatEnter
         * @function getTypeUrl
         * @memberof game.ChatEnter
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ChatEnter.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ChatEnter";
        };

        return ChatEnter;
    })();

    game.ChatLeave = (function() {

        /**
         * Properties of a ChatLeave.
         * @memberof game
         * @interface IChatLeave
         * @property {number|null} [chatId] ChatLeave chatId
         * @property {string|null} [btcAddress] ChatLeave btcAddress
         */

        /**
         * Constructs a new ChatLeave.
         * @memberof game
         * @classdesc Represents a ChatLeave.
         * @implements IChatLeave
         * @constructor
         * @param {game.IChatLeave=} [properties] Properties to set
         */
        function ChatLeave(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ChatLeave chatId.
         * @member {number} chatId
         * @memberof game.ChatLeave
         * @instance
         */
        ChatLeave.prototype.chatId = 0;

        /**
         * ChatLeave btcAddress.
         * @member {string} btcAddress
         * @memberof game.ChatLeave
         * @instance
         */
        ChatLeave.prototype.btcAddress = "";

        /**
         * Creates a new ChatLeave instance using the specified properties.
         * @function create
         * @memberof game.ChatLeave
         * @static
         * @param {game.IChatLeave=} [properties] Properties to set
         * @returns {game.ChatLeave} ChatLeave instance
         */
        ChatLeave.create = function create(properties) {
            return new ChatLeave(properties);
        };

        /**
         * Encodes the specified ChatLeave message. Does not implicitly {@link game.ChatLeave.verify|verify} messages.
         * @function encode
         * @memberof game.ChatLeave
         * @static
         * @param {game.IChatLeave} message ChatLeave message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatLeave.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.chatId != null && Object.hasOwnProperty.call(message, "chatId"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.chatId);
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.btcAddress);
            return writer;
        };

        /**
         * Encodes the specified ChatLeave message, length delimited. Does not implicitly {@link game.ChatLeave.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ChatLeave
         * @static
         * @param {game.IChatLeave} message ChatLeave message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatLeave.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ChatLeave message from the specified reader or buffer.
         * @function decode
         * @memberof game.ChatLeave
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ChatLeave} ChatLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatLeave.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ChatLeave();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.chatId = reader.int32();
                        break;
                    }
                case 2: {
                        message.btcAddress = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ChatLeave message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ChatLeave
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ChatLeave} ChatLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatLeave.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ChatLeave message.
         * @function verify
         * @memberof game.ChatLeave
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ChatLeave.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                if (!$util.isInteger(message.chatId))
                    return "chatId: integer expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            return null;
        };

        /**
         * Creates a ChatLeave message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ChatLeave
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ChatLeave} ChatLeave
         */
        ChatLeave.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ChatLeave)
                return object;
            var message = new $root.game.ChatLeave();
            if (object.chatId != null)
                message.chatId = object.chatId | 0;
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            return message;
        };

        /**
         * Creates a plain object from a ChatLeave message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ChatLeave
         * @static
         * @param {game.ChatLeave} message ChatLeave
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ChatLeave.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.chatId = 0;
                object.btcAddress = "";
            }
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                object.chatId = message.chatId;
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            return object;
        };

        /**
         * Converts this ChatLeave to JSON.
         * @function toJSON
         * @memberof game.ChatLeave
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ChatLeave.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ChatLeave
         * @function getTypeUrl
         * @memberof game.ChatLeave
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ChatLeave.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ChatLeave";
        };

        return ChatLeave;
    })();

    game.ChatMessage = (function() {

        /**
         * Properties of a ChatMessage.
         * @memberof game
         * @interface IChatMessage
         * @property {string|null} [uuid] ChatMessage uuid
         * @property {string|null} [playerId] ChatMessage playerId
         * @property {string|null} [content] ChatMessage content
         * @property {string|null} [replyTo] ChatMessage replyTo
         * @property {number|Long|null} [timestamp] ChatMessage timestamp
         * @property {number|null} [tabType] ChatMessage tabType
         */

        /**
         * Constructs a new ChatMessage.
         * @memberof game
         * @classdesc Represents a ChatMessage.
         * @implements IChatMessage
         * @constructor
         * @param {game.IChatMessage=} [properties] Properties to set
         */
        function ChatMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ChatMessage uuid.
         * @member {string} uuid
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.uuid = "";

        /**
         * ChatMessage playerId.
         * @member {string} playerId
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.playerId = "";

        /**
         * ChatMessage content.
         * @member {string} content
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.content = "";

        /**
         * ChatMessage replyTo.
         * @member {string} replyTo
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.replyTo = "";

        /**
         * ChatMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * ChatMessage tabType.
         * @member {number} tabType
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.tabType = 0;

        /**
         * Creates a new ChatMessage instance using the specified properties.
         * @function create
         * @memberof game.ChatMessage
         * @static
         * @param {game.IChatMessage=} [properties] Properties to set
         * @returns {game.ChatMessage} ChatMessage instance
         */
        ChatMessage.create = function create(properties) {
            return new ChatMessage(properties);
        };

        /**
         * Encodes the specified ChatMessage message. Does not implicitly {@link game.ChatMessage.verify|verify} messages.
         * @function encode
         * @memberof game.ChatMessage
         * @static
         * @param {game.IChatMessage} message ChatMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.uuid != null && Object.hasOwnProperty.call(message, "uuid"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.uuid);
            if (message.playerId != null && Object.hasOwnProperty.call(message, "playerId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.playerId);
            if (message.content != null && Object.hasOwnProperty.call(message, "content"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.content);
            if (message.replyTo != null && Object.hasOwnProperty.call(message, "replyTo"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.replyTo);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 5, wireType 0 =*/40).int64(message.timestamp);
            if (message.tabType != null && Object.hasOwnProperty.call(message, "tabType"))
                writer.uint32(/* id 6, wireType 0 =*/48).int32(message.tabType);
            return writer;
        };

        /**
         * Encodes the specified ChatMessage message, length delimited. Does not implicitly {@link game.ChatMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ChatMessage
         * @static
         * @param {game.IChatMessage} message ChatMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ChatMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.ChatMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ChatMessage} ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ChatMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.uuid = reader.string();
                        break;
                    }
                case 2: {
                        message.playerId = reader.string();
                        break;
                    }
                case 3: {
                        message.content = reader.string();
                        break;
                    }
                case 4: {
                        message.replyTo = reader.string();
                        break;
                    }
                case 5: {
                        message.timestamp = reader.int64();
                        break;
                    }
                case 6: {
                        message.tabType = reader.int32();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ChatMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ChatMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ChatMessage} ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ChatMessage message.
         * @function verify
         * @memberof game.ChatMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ChatMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.uuid != null && message.hasOwnProperty("uuid"))
                if (!$util.isString(message.uuid))
                    return "uuid: string expected";
            if (message.playerId != null && message.hasOwnProperty("playerId"))
                if (!$util.isString(message.playerId))
                    return "playerId: string expected";
            if (message.content != null && message.hasOwnProperty("content"))
                if (!$util.isString(message.content))
                    return "content: string expected";
            if (message.replyTo != null && message.hasOwnProperty("replyTo"))
                if (!$util.isString(message.replyTo))
                    return "replyTo: string expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            if (message.tabType != null && message.hasOwnProperty("tabType"))
                if (!$util.isInteger(message.tabType))
                    return "tabType: integer expected";
            return null;
        };

        /**
         * Creates a ChatMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ChatMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ChatMessage} ChatMessage
         */
        ChatMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ChatMessage)
                return object;
            var message = new $root.game.ChatMessage();
            if (object.uuid != null)
                message.uuid = String(object.uuid);
            if (object.playerId != null)
                message.playerId = String(object.playerId);
            if (object.content != null)
                message.content = String(object.content);
            if (object.replyTo != null)
                message.replyTo = String(object.replyTo);
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            if (object.tabType != null)
                message.tabType = object.tabType | 0;
            return message;
        };

        /**
         * Creates a plain object from a ChatMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ChatMessage
         * @static
         * @param {game.ChatMessage} message ChatMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ChatMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.uuid = "";
                object.playerId = "";
                object.content = "";
                object.replyTo = "";
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
                object.tabType = 0;
            }
            if (message.uuid != null && message.hasOwnProperty("uuid"))
                object.uuid = message.uuid;
            if (message.playerId != null && message.hasOwnProperty("playerId"))
                object.playerId = message.playerId;
            if (message.content != null && message.hasOwnProperty("content"))
                object.content = message.content;
            if (message.replyTo != null && message.hasOwnProperty("replyTo"))
                object.replyTo = message.replyTo;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            if (message.tabType != null && message.hasOwnProperty("tabType"))
                object.tabType = message.tabType;
            return object;
        };

        /**
         * Converts this ChatMessage to JSON.
         * @function toJSON
         * @memberof game.ChatMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ChatMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ChatMessage
         * @function getTypeUrl
         * @memberof game.ChatMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ChatMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ChatMessage";
        };

        return ChatMessage;
    })();

    game.RedPacketUpdate = (function() {

        /**
         * Properties of a RedPacketUpdate.
         * @memberof game
         * @interface IRedPacketUpdate
         * @property {string|null} [packetId] RedPacketUpdate packetId
         * @property {string|null} [creatorAddress] RedPacketUpdate creatorAddress
         * @property {number|null} [amount] RedPacketUpdate amount
         * @property {number|null} [count] RedPacketUpdate count
         * @property {number|null} [remaining] RedPacketUpdate remaining
         * @property {string|null} [status] RedPacketUpdate status
         * @property {number|Long|null} [createdAt] RedPacketUpdate createdAt
         * @property {number|Long|null} [expiresAt] RedPacketUpdate expiresAt
         */

        /**
         * Constructs a new RedPacketUpdate.
         * @memberof game
         * @classdesc Represents a RedPacketUpdate.
         * @implements IRedPacketUpdate
         * @constructor
         * @param {game.IRedPacketUpdate=} [properties] Properties to set
         */
        function RedPacketUpdate(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * RedPacketUpdate packetId.
         * @member {string} packetId
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.packetId = "";

        /**
         * RedPacketUpdate creatorAddress.
         * @member {string} creatorAddress
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.creatorAddress = "";

        /**
         * RedPacketUpdate amount.
         * @member {number} amount
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.amount = 0;

        /**
         * RedPacketUpdate count.
         * @member {number} count
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.count = 0;

        /**
         * RedPacketUpdate remaining.
         * @member {number} remaining
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.remaining = 0;

        /**
         * RedPacketUpdate status.
         * @member {string} status
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.status = "";

        /**
         * RedPacketUpdate createdAt.
         * @member {number|Long} createdAt
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.createdAt = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * RedPacketUpdate expiresAt.
         * @member {number|Long} expiresAt
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.expiresAt = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new RedPacketUpdate instance using the specified properties.
         * @function create
         * @memberof game.RedPacketUpdate
         * @static
         * @param {game.IRedPacketUpdate=} [properties] Properties to set
         * @returns {game.RedPacketUpdate} RedPacketUpdate instance
         */
        RedPacketUpdate.create = function create(properties) {
            return new RedPacketUpdate(properties);
        };

        /**
         * Encodes the specified RedPacketUpdate message. Does not implicitly {@link game.RedPacketUpdate.verify|verify} messages.
         * @function encode
         * @memberof game.RedPacketUpdate
         * @static
         * @param {game.IRedPacketUpdate} message RedPacketUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketUpdate.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.packetId != null && Object.hasOwnProperty.call(message, "packetId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.packetId);
            if (message.creatorAddress != null && Object.hasOwnProperty.call(message, "creatorAddress"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.creatorAddress);
            if (message.amount != null && Object.hasOwnProperty.call(message, "amount"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.amount);
            if (message.count != null && Object.hasOwnProperty.call(message, "count"))
                writer.uint32(/* id 4, wireType 0 =*/32).int32(message.count);
            if (message.remaining != null && Object.hasOwnProperty.call(message, "remaining"))
                writer.uint32(/* id 5, wireType 0 =*/40).int32(message.remaining);
            if (message.status != null && Object.hasOwnProperty.call(message, "status"))
                writer.uint32(/* id 6, wireType 2 =*/50).string(message.status);
            if (message.createdAt != null && Object.hasOwnProperty.call(message, "createdAt"))
                writer.uint32(/* id 7, wireType 0 =*/56).int64(message.createdAt);
            if (message.expiresAt != null && Object.hasOwnProperty.call(message, "expiresAt"))
                writer.uint32(/* id 8, wireType 0 =*/64).int64(message.expiresAt);
            return writer;
        };

        /**
         * Encodes the specified RedPacketUpdate message, length delimited. Does not implicitly {@link game.RedPacketUpdate.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.RedPacketUpdate
         * @static
         * @param {game.IRedPacketUpdate} message RedPacketUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketUpdate.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a RedPacketUpdate message from the specified reader or buffer.
         * @function decode
         * @memberof game.RedPacketUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.RedPacketUpdate} RedPacketUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketUpdate.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.RedPacketUpdate();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.packetId = reader.string();
                        break;
                    }
                case 2: {
                        message.creatorAddress = reader.string();
                        break;
                    }
                case 3: {
                        message.amount = reader.float();
                        break;
                    }
                case 4: {
                        message.count = reader.int32();
                        break;
                    }
                case 5: {
                        message.remaining = reader.int32();
                        break;
                    }
                case 6: {
                        message.status = reader.string();
                        break;
                    }
                case 7: {
                        message.createdAt = reader.int64();
                        break;
                    }
                case 8: {
                        message.expiresAt = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a RedPacketUpdate message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.RedPacketUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.RedPacketUpdate} RedPacketUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketUpdate.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a RedPacketUpdate message.
         * @function verify
         * @memberof game.RedPacketUpdate
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        RedPacketUpdate.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.packetId != null && message.hasOwnProperty("packetId"))
                if (!$util.isString(message.packetId))
                    return "packetId: string expected";
            if (message.creatorAddress != null && message.hasOwnProperty("creatorAddress"))
                if (!$util.isString(message.creatorAddress))
                    return "creatorAddress: string expected";
            if (message.amount != null && message.hasOwnProperty("amount"))
                if (typeof message.amount !== "number")
                    return "amount: number expected";
            if (message.count != null && message.hasOwnProperty("count"))
                if (!$util.isInteger(message.count))
                    return "count: integer expected";
            if (message.remaining != null && message.hasOwnProperty("remaining"))
                if (!$util.isInteger(message.remaining))
                    return "remaining: integer expected";
            if (message.status != null && message.hasOwnProperty("status"))
                if (!$util.isString(message.status))
                    return "status: string expected";
            if (message.createdAt != null && message.hasOwnProperty("createdAt"))
                if (!$util.isInteger(message.createdAt) && !(message.createdAt && $util.isInteger(message.createdAt.low) && $util.isInteger(message.createdAt.high)))
                    return "createdAt: integer|Long expected";
            if (message.expiresAt != null && message.hasOwnProperty("expiresAt"))
                if (!$util.isInteger(message.expiresAt) && !(message.expiresAt && $util.isInteger(message.expiresAt.low) && $util.isInteger(message.expiresAt.high)))
                    return "expiresAt: integer|Long expected";
            return null;
        };

        /**
         * Creates a RedPacketUpdate message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.RedPacketUpdate
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.RedPacketUpdate} RedPacketUpdate
         */
        RedPacketUpdate.fromObject = function fromObject(object) {
            if (object instanceof $root.game.RedPacketUpdate)
                return object;
            var message = new $root.game.RedPacketUpdate();
            if (object.packetId != null)
                message.packetId = String(object.packetId);
            if (object.creatorAddress != null)
                message.creatorAddress = String(object.creatorAddress);
            if (object.amount != null)
                message.amount = Number(object.amount);
            if (object.count != null)
                message.count = object.count | 0;
            if (object.remaining != null)
                message.remaining = object.remaining | 0;
            if (object.status != null)
                message.status = String(object.status);
            if (object.createdAt != null)
                if ($util.Long)
                    (message.createdAt = $util.Long.fromValue(object.createdAt)).unsigned = false;
                else if (typeof object.createdAt === "string")
                    message.createdAt = parseInt(object.createdAt, 10);
                else if (typeof object.createdAt === "number")
                    message.createdAt = object.createdAt;
                else if (typeof object.createdAt === "object")
                    message.createdAt = new $util.LongBits(object.createdAt.low >>> 0, object.createdAt.high >>> 0).toNumber();
            if (object.expiresAt != null)
                if ($util.Long)
                    (message.expiresAt = $util.Long.fromValue(object.expiresAt)).unsigned = false;
                else if (typeof object.expiresAt === "string")
                    message.expiresAt = parseInt(object.expiresAt, 10);
                else if (typeof object.expiresAt === "number")
                    message.expiresAt = object.expiresAt;
                else if (typeof object.expiresAt === "object")
                    message.expiresAt = new $util.LongBits(object.expiresAt.low >>> 0, object.expiresAt.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a RedPacketUpdate message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.RedPacketUpdate
         * @static
         * @param {game.RedPacketUpdate} message RedPacketUpdate
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        RedPacketUpdate.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.packetId = "";
                object.creatorAddress = "";
                object.amount = 0;
                object.count = 0;
                object.remaining = 0;
                object.status = "";
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.createdAt = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.createdAt = options.longs === String ? "0" : 0;
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.expiresAt = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.expiresAt = options.longs === String ? "0" : 0;
            }
            if (message.packetId != null && message.hasOwnProperty("packetId"))
                object.packetId = message.packetId;
            if (message.creatorAddress != null && message.hasOwnProperty("creatorAddress"))
                object.creatorAddress = message.creatorAddress;
            if (message.amount != null && message.hasOwnProperty("amount"))
                object.amount = options.json && !isFinite(message.amount) ? String(message.amount) : message.amount;
            if (message.count != null && message.hasOwnProperty("count"))
                object.count = message.count;
            if (message.remaining != null && message.hasOwnProperty("remaining"))
                object.remaining = message.remaining;
            if (message.status != null && message.hasOwnProperty("status"))
                object.status = message.status;
            if (message.createdAt != null && message.hasOwnProperty("createdAt"))
                if (typeof message.createdAt === "number")
                    object.createdAt = options.longs === String ? String(message.createdAt) : message.createdAt;
                else
                    object.createdAt = options.longs === String ? $util.Long.prototype.toString.call(message.createdAt) : options.longs === Number ? new $util.LongBits(message.createdAt.low >>> 0, message.createdAt.high >>> 0).toNumber() : message.createdAt;
            if (message.expiresAt != null && message.hasOwnProperty("expiresAt"))
                if (typeof message.expiresAt === "number")
                    object.expiresAt = options.longs === String ? String(message.expiresAt) : message.expiresAt;
                else
                    object.expiresAt = options.longs === String ? $util.Long.prototype.toString.call(message.expiresAt) : options.longs === Number ? new $util.LongBits(message.expiresAt.low >>> 0, message.expiresAt.high >>> 0).toNumber() : message.expiresAt;
            return object;
        };

        /**
         * Converts this RedPacketUpdate to JSON.
         * @function toJSON
         * @memberof game.RedPacketUpdate
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        RedPacketUpdate.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for RedPacketUpdate
         * @function getTypeUrl
         * @memberof game.RedPacketUpdate
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        RedPacketUpdate.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.RedPacketUpdate";
        };

        return RedPacketUpdate;
    })();

    game.RedPacketReward = (function() {

        /**
         * Properties of a RedPacketReward.
         * @memberof game
         * @interface IRedPacketReward
         * @property {string|null} [packetId] RedPacketReward packetId
         * @property {string|null} [receiverAddress] RedPacketReward receiverAddress
         * @property {number|null} [amount] RedPacketReward amount
         * @property {number|Long|null} [receivedAt] RedPacketReward receivedAt
         */

        /**
         * Constructs a new RedPacketReward.
         * @memberof game
         * @classdesc Represents a RedPacketReward.
         * @implements IRedPacketReward
         * @constructor
         * @param {game.IRedPacketReward=} [properties] Properties to set
         */
        function RedPacketReward(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * RedPacketReward packetId.
         * @member {string} packetId
         * @memberof game.RedPacketReward
         * @instance
         */
        RedPacketReward.prototype.packetId = "";

        /**
         * RedPacketReward receiverAddress.
         * @member {string} receiverAddress
         * @memberof game.RedPacketReward
         * @instance
         */
        RedPacketReward.prototype.receiverAddress = "";

        /**
         * RedPacketReward amount.
         * @member {number} amount
         * @memberof game.RedPacketReward
         * @instance
         */
        RedPacketReward.prototype.amount = 0;

        /**
         * RedPacketReward receivedAt.
         * @member {number|Long} receivedAt
         * @memberof game.RedPacketReward
         * @instance
         */
        RedPacketReward.prototype.receivedAt = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new RedPacketReward instance using the specified properties.
         * @function create
         * @memberof game.RedPacketReward
         * @static
         * @param {game.IRedPacketReward=} [properties] Properties to set
         * @returns {game.RedPacketReward} RedPacketReward instance
         */
        RedPacketReward.create = function create(properties) {
            return new RedPacketReward(properties);
        };

        /**
         * Encodes the specified RedPacketReward message. Does not implicitly {@link game.RedPacketReward.verify|verify} messages.
         * @function encode
         * @memberof game.RedPacketReward
         * @static
         * @param {game.IRedPacketReward} message RedPacketReward message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketReward.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.packetId != null && Object.hasOwnProperty.call(message, "packetId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.packetId);
            if (message.receiverAddress != null && Object.hasOwnProperty.call(message, "receiverAddress"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.receiverAddress);
            if (message.amount != null && Object.hasOwnProperty.call(message, "amount"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.amount);
            if (message.receivedAt != null && Object.hasOwnProperty.call(message, "receivedAt"))
                writer.uint32(/* id 4, wireType 0 =*/32).int64(message.receivedAt);
            return writer;
        };

        /**
         * Encodes the specified RedPacketReward message, length delimited. Does not implicitly {@link game.RedPacketReward.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.RedPacketReward
         * @static
         * @param {game.IRedPacketReward} message RedPacketReward message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketReward.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a RedPacketReward message from the specified reader or buffer.
         * @function decode
         * @memberof game.RedPacketReward
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.RedPacketReward} RedPacketReward
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketReward.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.RedPacketReward();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.packetId = reader.string();
                        break;
                    }
                case 2: {
                        message.receiverAddress = reader.string();
                        break;
                    }
                case 3: {
                        message.amount = reader.float();
                        break;
                    }
                case 4: {
                        message.receivedAt = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a RedPacketReward message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.RedPacketReward
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.RedPacketReward} RedPacketReward
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketReward.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a RedPacketReward message.
         * @function verify
         * @memberof game.RedPacketReward
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        RedPacketReward.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.packetId != null && message.hasOwnProperty("packetId"))
                if (!$util.isString(message.packetId))
                    return "packetId: string expected";
            if (message.receiverAddress != null && message.hasOwnProperty("receiverAddress"))
                if (!$util.isString(message.receiverAddress))
                    return "receiverAddress: string expected";
            if (message.amount != null && message.hasOwnProperty("amount"))
                if (typeof message.amount !== "number")
                    return "amount: number expected";
            if (message.receivedAt != null && message.hasOwnProperty("receivedAt"))
                if (!$util.isInteger(message.receivedAt) && !(message.receivedAt && $util.isInteger(message.receivedAt.low) && $util.isInteger(message.receivedAt.high)))
                    return "receivedAt: integer|Long expected";
            return null;
        };

        /**
         * Creates a RedPacketReward message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.RedPacketReward
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.RedPacketReward} RedPacketReward
         */
        RedPacketReward.fromObject = function fromObject(object) {
            if (object instanceof $root.game.RedPacketReward)
                return object;
            var message = new $root.game.RedPacketReward();
            if (object.packetId != null)
                message.packetId = String(object.packetId);
            if (object.receiverAddress != null)
                message.receiverAddress = String(object.receiverAddress);
            if (object.amount != null)
                message.amount = Number(object.amount);
            if (object.receivedAt != null)
                if ($util.Long)
                    (message.receivedAt = $util.Long.fromValue(object.receivedAt)).unsigned = false;
                else if (typeof object.receivedAt === "string")
                    message.receivedAt = parseInt(object.receivedAt, 10);
                else if (typeof object.receivedAt === "number")
                    message.receivedAt = object.receivedAt;
                else if (typeof object.receivedAt === "object")
                    message.receivedAt = new $util.LongBits(object.receivedAt.low >>> 0, object.receivedAt.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a RedPacketReward message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.RedPacketReward
         * @static
         * @param {game.RedPacketReward} message RedPacketReward
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        RedPacketReward.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.packetId = "";
                object.receiverAddress = "";
                object.amount = 0;
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.receivedAt = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.receivedAt = options.longs === String ? "0" : 0;
            }
            if (message.packetId != null && message.hasOwnProperty("packetId"))
                object.packetId = message.packetId;
            if (message.receiverAddress != null && message.hasOwnProperty("receiverAddress"))
                object.receiverAddress = message.receiverAddress;
            if (message.amount != null && message.hasOwnProperty("amount"))
                object.amount = options.json && !isFinite(message.amount) ? String(message.amount) : message.amount;
            if (message.receivedAt != null && message.hasOwnProperty("receivedAt"))
                if (typeof message.receivedAt === "number")
                    object.receivedAt = options.longs === String ? String(message.receivedAt) : message.receivedAt;
                else
                    object.receivedAt = options.longs === String ? $util.Long.prototype.toString.call(message.receivedAt) : options.longs === Number ? new $util.LongBits(message.receivedAt.low >>> 0, message.receivedAt.high >>> 0).toNumber() : message.receivedAt;
            return object;
        };

        /**
         * Converts this RedPacketReward to JSON.
         * @function toJSON
         * @memberof game.RedPacketReward
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        RedPacketReward.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for RedPacketReward
         * @function getTypeUrl
         * @memberof game.RedPacketReward
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        RedPacketReward.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.RedPacketReward";
        };

        return RedPacketReward;
    })();

    game.PlayerActionMessage = (function() {

        /**
         * Properties of a PlayerActionMessage.
         * @memberof game
         * @interface IPlayerActionMessage
         * @property {number|null} [pid] PlayerActionMessage pid
         * @property {Uint8Array|null} [actionData] PlayerActionMessage actionData
         * @property {string|null} [btcAddress] PlayerActionMessage btcAddress
         * @property {number|Long|null} [timestamp] PlayerActionMessage timestamp
         */

        /**
         * Constructs a new PlayerActionMessage.
         * @memberof game
         * @classdesc Represents a PlayerActionMessage.
         * @implements IPlayerActionMessage
         * @constructor
         * @param {game.IPlayerActionMessage=} [properties] Properties to set
         */
        function PlayerActionMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerActionMessage pid.
         * @member {number} pid
         * @memberof game.PlayerActionMessage
         * @instance
         */
        PlayerActionMessage.prototype.pid = 0;

        /**
         * PlayerActionMessage actionData.
         * @member {Uint8Array} actionData
         * @memberof game.PlayerActionMessage
         * @instance
         */
        PlayerActionMessage.prototype.actionData = $util.newBuffer([]);

        /**
         * PlayerActionMessage btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerActionMessage
         * @instance
         */
        PlayerActionMessage.prototype.btcAddress = "";

        /**
         * PlayerActionMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof game.PlayerActionMessage
         * @instance
         */
        PlayerActionMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new PlayerActionMessage instance using the specified properties.
         * @function create
         * @memberof game.PlayerActionMessage
         * @static
         * @param {game.IPlayerActionMessage=} [properties] Properties to set
         * @returns {game.PlayerActionMessage} PlayerActionMessage instance
         */
        PlayerActionMessage.create = function create(properties) {
            return new PlayerActionMessage(properties);
        };

        /**
         * Encodes the specified PlayerActionMessage message. Does not implicitly {@link game.PlayerActionMessage.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerActionMessage
         * @static
         * @param {game.IPlayerActionMessage} message PlayerActionMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerActionMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.pid != null && Object.hasOwnProperty.call(message, "pid"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.pid);
            if (message.actionData != null && Object.hasOwnProperty.call(message, "actionData"))
                writer.uint32(/* id 2, wireType 2 =*/18).bytes(message.actionData);
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.btcAddress);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 4, wireType 0 =*/32).int64(message.timestamp);
            return writer;
        };

        /**
         * Encodes the specified PlayerActionMessage message, length delimited. Does not implicitly {@link game.PlayerActionMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerActionMessage
         * @static
         * @param {game.IPlayerActionMessage} message PlayerActionMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerActionMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerActionMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerActionMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerActionMessage} PlayerActionMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerActionMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerActionMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.pid = reader.int32();
                        break;
                    }
                case 2: {
                        message.actionData = reader.bytes();
                        break;
                    }
                case 3: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 4: {
                        message.timestamp = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerActionMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerActionMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerActionMessage} PlayerActionMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerActionMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerActionMessage message.
         * @function verify
         * @memberof game.PlayerActionMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerActionMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.pid != null && message.hasOwnProperty("pid"))
                if (!$util.isInteger(message.pid))
                    return "pid: integer expected";
            if (message.actionData != null && message.hasOwnProperty("actionData"))
                if (!(message.actionData && typeof message.actionData.length === "number" || $util.isString(message.actionData)))
                    return "actionData: buffer expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            return null;
        };

        /**
         * Creates a PlayerActionMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerActionMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerActionMessage} PlayerActionMessage
         */
        PlayerActionMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerActionMessage)
                return object;
            var message = new $root.game.PlayerActionMessage();
            if (object.pid != null)
                message.pid = object.pid | 0;
            if (object.actionData != null)
                if (typeof object.actionData === "string")
                    $util.base64.decode(object.actionData, message.actionData = $util.newBuffer($util.base64.length(object.actionData)), 0);
                else if (object.actionData.length >= 0)
                    message.actionData = object.actionData;
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a PlayerActionMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerActionMessage
         * @static
         * @param {game.PlayerActionMessage} message PlayerActionMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerActionMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.pid = 0;
                if (options.bytes === String)
                    object.actionData = "";
                else {
                    object.actionData = [];
                    if (options.bytes !== Array)
                        object.actionData = $util.newBuffer(object.actionData);
                }
                object.btcAddress = "";
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
            }
            if (message.pid != null && message.hasOwnProperty("pid"))
                object.pid = message.pid;
            if (message.actionData != null && message.hasOwnProperty("actionData"))
                object.actionData = options.bytes === String ? $util.base64.encode(message.actionData, 0, message.actionData.length) : options.bytes === Array ? Array.prototype.slice.call(message.actionData) : message.actionData;
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            return object;
        };

        /**
         * Converts this PlayerActionMessage to JSON.
         * @function toJSON
         * @memberof game.PlayerActionMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerActionMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerActionMessage
         * @function getTypeUrl
         * @memberof game.PlayerActionMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerActionMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerActionMessage";
        };

        return PlayerActionMessage;
    })();

    game.PlayerRequestMessage = (function() {

        /**
         * Properties of a PlayerRequestMessage.
         * @memberof game
         * @interface IPlayerRequestMessage
         * @property {number|null} [pid] PlayerRequestMessage pid
         * @property {Uint8Array|null} [requestData] PlayerRequestMessage requestData
         * @property {string|null} [btcAddress] PlayerRequestMessage btcAddress
         * @property {number|Long|null} [timestamp] PlayerRequestMessage timestamp
         */

        /**
         * Constructs a new PlayerRequestMessage.
         * @memberof game
         * @classdesc Represents a PlayerRequestMessage.
         * @implements IPlayerRequestMessage
         * @constructor
         * @param {game.IPlayerRequestMessage=} [properties] Properties to set
         */
        function PlayerRequestMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerRequestMessage pid.
         * @member {number} pid
         * @memberof game.PlayerRequestMessage
         * @instance
         */
        PlayerRequestMessage.prototype.pid = 0;

        /**
         * PlayerRequestMessage requestData.
         * @member {Uint8Array} requestData
         * @memberof game.PlayerRequestMessage
         * @instance
         */
        PlayerRequestMessage.prototype.requestData = $util.newBuffer([]);

        /**
         * PlayerRequestMessage btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerRequestMessage
         * @instance
         */
        PlayerRequestMessage.prototype.btcAddress = "";

        /**
         * PlayerRequestMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof game.PlayerRequestMessage
         * @instance
         */
        PlayerRequestMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new PlayerRequestMessage instance using the specified properties.
         * @function create
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {game.IPlayerRequestMessage=} [properties] Properties to set
         * @returns {game.PlayerRequestMessage} PlayerRequestMessage instance
         */
        PlayerRequestMessage.create = function create(properties) {
            return new PlayerRequestMessage(properties);
        };

        /**
         * Encodes the specified PlayerRequestMessage message. Does not implicitly {@link game.PlayerRequestMessage.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {game.IPlayerRequestMessage} message PlayerRequestMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerRequestMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.pid != null && Object.hasOwnProperty.call(message, "pid"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.pid);
            if (message.requestData != null && Object.hasOwnProperty.call(message, "requestData"))
                writer.uint32(/* id 2, wireType 2 =*/18).bytes(message.requestData);
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.btcAddress);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 4, wireType 0 =*/32).int64(message.timestamp);
            return writer;
        };

        /**
         * Encodes the specified PlayerRequestMessage message, length delimited. Does not implicitly {@link game.PlayerRequestMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {game.IPlayerRequestMessage} message PlayerRequestMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerRequestMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerRequestMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerRequestMessage} PlayerRequestMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerRequestMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerRequestMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.pid = reader.int32();
                        break;
                    }
                case 2: {
                        message.requestData = reader.bytes();
                        break;
                    }
                case 3: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 4: {
                        message.timestamp = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerRequestMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerRequestMessage} PlayerRequestMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerRequestMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerRequestMessage message.
         * @function verify
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerRequestMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.pid != null && message.hasOwnProperty("pid"))
                if (!$util.isInteger(message.pid))
                    return "pid: integer expected";
            if (message.requestData != null && message.hasOwnProperty("requestData"))
                if (!(message.requestData && typeof message.requestData.length === "number" || $util.isString(message.requestData)))
                    return "requestData: buffer expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            return null;
        };

        /**
         * Creates a PlayerRequestMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerRequestMessage} PlayerRequestMessage
         */
        PlayerRequestMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerRequestMessage)
                return object;
            var message = new $root.game.PlayerRequestMessage();
            if (object.pid != null)
                message.pid = object.pid | 0;
            if (object.requestData != null)
                if (typeof object.requestData === "string")
                    $util.base64.decode(object.requestData, message.requestData = $util.newBuffer($util.base64.length(object.requestData)), 0);
                else if (object.requestData.length >= 0)
                    message.requestData = object.requestData;
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a PlayerRequestMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {game.PlayerRequestMessage} message PlayerRequestMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerRequestMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.pid = 0;
                if (options.bytes === String)
                    object.requestData = "";
                else {
                    object.requestData = [];
                    if (options.bytes !== Array)
                        object.requestData = $util.newBuffer(object.requestData);
                }
                object.btcAddress = "";
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
            }
            if (message.pid != null && message.hasOwnProperty("pid"))
                object.pid = message.pid;
            if (message.requestData != null && message.hasOwnProperty("requestData"))
                object.requestData = options.bytes === String ? $util.base64.encode(message.requestData, 0, message.requestData.length) : options.bytes === Array ? Array.prototype.slice.call(message.requestData) : message.requestData;
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            return object;
        };

        /**
         * Converts this PlayerRequestMessage to JSON.
         * @function toJSON
         * @memberof game.PlayerRequestMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerRequestMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerRequestMessage
         * @function getTypeUrl
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerRequestMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerRequestMessage";
        };

        return PlayerRequestMessage;
    })();

    game.PlayerChatMessage = (function() {

        /**
         * Properties of a PlayerChatMessage.
         * @memberof game
         * @interface IPlayerChatMessage
         * @property {number|null} [chatId] PlayerChatMessage chatId
         * @property {number|null} [pid] PlayerChatMessage pid
         * @property {Uint8Array|null} [chatData] PlayerChatMessage chatData
         * @property {string|null} [btcAddress] PlayerChatMessage btcAddress
         * @property {number|Long|null} [timestamp] PlayerChatMessage timestamp
         */

        /**
         * Constructs a new PlayerChatMessage.
         * @memberof game
         * @classdesc Represents a PlayerChatMessage.
         * @implements IPlayerChatMessage
         * @constructor
         * @param {game.IPlayerChatMessage=} [properties] Properties to set
         */
        function PlayerChatMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerChatMessage chatId.
         * @member {number} chatId
         * @memberof game.PlayerChatMessage
         * @instance
         */
        PlayerChatMessage.prototype.chatId = 0;

        /**
         * PlayerChatMessage pid.
         * @member {number} pid
         * @memberof game.PlayerChatMessage
         * @instance
         */
        PlayerChatMessage.prototype.pid = 0;

        /**
         * PlayerChatMessage chatData.
         * @member {Uint8Array} chatData
         * @memberof game.PlayerChatMessage
         * @instance
         */
        PlayerChatMessage.prototype.chatData = $util.newBuffer([]);

        /**
         * PlayerChatMessage btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerChatMessage
         * @instance
         */
        PlayerChatMessage.prototype.btcAddress = "";

        /**
         * PlayerChatMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof game.PlayerChatMessage
         * @instance
         */
        PlayerChatMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new PlayerChatMessage instance using the specified properties.
         * @function create
         * @memberof game.PlayerChatMessage
         * @static
         * @param {game.IPlayerChatMessage=} [properties] Properties to set
         * @returns {game.PlayerChatMessage} PlayerChatMessage instance
         */
        PlayerChatMessage.create = function create(properties) {
            return new PlayerChatMessage(properties);
        };

        /**
         * Encodes the specified PlayerChatMessage message. Does not implicitly {@link game.PlayerChatMessage.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerChatMessage
         * @static
         * @param {game.IPlayerChatMessage} message PlayerChatMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerChatMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.chatId != null && Object.hasOwnProperty.call(message, "chatId"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.chatId);
            if (message.pid != null && Object.hasOwnProperty.call(message, "pid"))
                writer.uint32(/* id 2, wireType 0 =*/16).int32(message.pid);
            if (message.chatData != null && Object.hasOwnProperty.call(message, "chatData"))
                writer.uint32(/* id 3, wireType 2 =*/26).bytes(message.chatData);
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.btcAddress);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 5, wireType 0 =*/40).int64(message.timestamp);
            return writer;
        };

        /**
         * Encodes the specified PlayerChatMessage message, length delimited. Does not implicitly {@link game.PlayerChatMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerChatMessage
         * @static
         * @param {game.IPlayerChatMessage} message PlayerChatMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerChatMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerChatMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerChatMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerChatMessage} PlayerChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerChatMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerChatMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.chatId = reader.int32();
                        break;
                    }
                case 2: {
                        message.pid = reader.int32();
                        break;
                    }
                case 3: {
                        message.chatData = reader.bytes();
                        break;
                    }
                case 4: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 5: {
                        message.timestamp = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerChatMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerChatMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerChatMessage} PlayerChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerChatMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerChatMessage message.
         * @function verify
         * @memberof game.PlayerChatMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerChatMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                if (!$util.isInteger(message.chatId))
                    return "chatId: integer expected";
            if (message.pid != null && message.hasOwnProperty("pid"))
                if (!$util.isInteger(message.pid))
                    return "pid: integer expected";
            if (message.chatData != null && message.hasOwnProperty("chatData"))
                if (!(message.chatData && typeof message.chatData.length === "number" || $util.isString(message.chatData)))
                    return "chatData: buffer expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            return null;
        };

        /**
         * Creates a PlayerChatMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerChatMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerChatMessage} PlayerChatMessage
         */
        PlayerChatMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerChatMessage)
                return object;
            var message = new $root.game.PlayerChatMessage();
            if (object.chatId != null)
                message.chatId = object.chatId | 0;
            if (object.pid != null)
                message.pid = object.pid | 0;
            if (object.chatData != null)
                if (typeof object.chatData === "string")
                    $util.base64.decode(object.chatData, message.chatData = $util.newBuffer($util.base64.length(object.chatData)), 0);
                else if (object.chatData.length >= 0)
                    message.chatData = object.chatData;
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a PlayerChatMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerChatMessage
         * @static
         * @param {game.PlayerChatMessage} message PlayerChatMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerChatMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.chatId = 0;
                object.pid = 0;
                if (options.bytes === String)
                    object.chatData = "";
                else {
                    object.chatData = [];
                    if (options.bytes !== Array)
                        object.chatData = $util.newBuffer(object.chatData);
                }
                object.btcAddress = "";
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
            }
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                object.chatId = message.chatId;
            if (message.pid != null && message.hasOwnProperty("pid"))
                object.pid = message.pid;
            if (message.chatData != null && message.hasOwnProperty("chatData"))
                object.chatData = options.bytes === String ? $util.base64.encode(message.chatData, 0, message.chatData.length) : options.bytes === Array ? Array.prototype.slice.call(message.chatData) : message.chatData;
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            return object;
        };

        /**
         * Converts this PlayerChatMessage to JSON.
         * @function toJSON
         * @memberof game.PlayerChatMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerChatMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerChatMessage
         * @function getTypeUrl
         * @memberof game.PlayerChatMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerChatMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerChatMessage";
        };

        return PlayerChatMessage;
    })();

    game.ClientPlayerPosition = (function() {

        /**
         * Properties of a ClientPlayerPosition.
         * @memberof game
         * @interface IClientPlayerPosition
         * @property {number|null} [x] ClientPlayerPosition x
         * @property {number|null} [y] ClientPlayerPosition y
         * @property {number|null} [z] ClientPlayerPosition z
         * @property {number|null} [rotationX] ClientPlayerPosition rotationX
         * @property {number|null} [rotationY] ClientPlayerPosition rotationY
         * @property {number|null} [rotationZ] ClientPlayerPosition rotationZ
         * @property {number|null} [rotationW] ClientPlayerPosition rotationW
         */

        /**
         * Constructs a new ClientPlayerPosition.
         * @memberof game
         * @classdesc Represents a ClientPlayerPosition.
         * @implements IClientPlayerPosition
         * @constructor
         * @param {game.IClientPlayerPosition=} [properties] Properties to set
         */
        function ClientPlayerPosition(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ClientPlayerPosition x.
         * @member {number} x
         * @memberof game.ClientPlayerPosition
         * @instance
         */
        ClientPlayerPosition.prototype.x = 0;

        /**
         * ClientPlayerPosition y.
         * @member {number} y
         * @memberof game.ClientPlayerPosition
         * @instance
         */
        ClientPlayerPosition.prototype.y = 0;

        /**
         * ClientPlayerPosition z.
         * @member {number} z
         * @memberof game.ClientPlayerPosition
         * @instance
         */
        ClientPlayerPosition.prototype.z = 0;

        /**
         * ClientPlayerPosition rotationX.
         * @member {number} rotationX
         * @memberof game.ClientPlayerPosition
         * @instance
         */
        ClientPlayerPosition.prototype.rotationX = 0;

        /**
         * ClientPlayerPosition rotationY.
         * @member {number} rotationY
         * @memberof game.ClientPlayerPosition
         * @instance
         */
        ClientPlayerPosition.prototype.rotationY = 0;

        /**
         * ClientPlayerPosition rotationZ.
         * @member {number} rotationZ
         * @memberof game.ClientPlayerPosition
         * @instance
         */
        ClientPlayerPosition.prototype.rotationZ = 0;

        /**
         * ClientPlayerPosition rotationW.
         * @member {number} rotationW
         * @memberof game.ClientPlayerPosition
         * @instance
         */
        ClientPlayerPosition.prototype.rotationW = 0;

        /**
         * Creates a new ClientPlayerPosition instance using the specified properties.
         * @function create
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {game.IClientPlayerPosition=} [properties] Properties to set
         * @returns {game.ClientPlayerPosition} ClientPlayerPosition instance
         */
        ClientPlayerPosition.create = function create(properties) {
            return new ClientPlayerPosition(properties);
        };

        /**
         * Encodes the specified ClientPlayerPosition message. Does not implicitly {@link game.ClientPlayerPosition.verify|verify} messages.
         * @function encode
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {game.IClientPlayerPosition} message ClientPlayerPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerPosition.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.x != null && Object.hasOwnProperty.call(message, "x"))
                writer.uint32(/* id 1, wireType 5 =*/13).float(message.x);
            if (message.y != null && Object.hasOwnProperty.call(message, "y"))
                writer.uint32(/* id 2, wireType 5 =*/21).float(message.y);
            if (message.z != null && Object.hasOwnProperty.call(message, "z"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.z);
            if (message.rotationX != null && Object.hasOwnProperty.call(message, "rotationX"))
                writer.uint32(/* id 4, wireType 5 =*/37).float(message.rotationX);
            if (message.rotationY != null && Object.hasOwnProperty.call(message, "rotationY"))
                writer.uint32(/* id 5, wireType 5 =*/45).float(message.rotationY);
            if (message.rotationZ != null && Object.hasOwnProperty.call(message, "rotationZ"))
                writer.uint32(/* id 6, wireType 5 =*/53).float(message.rotationZ);
            if (message.rotationW != null && Object.hasOwnProperty.call(message, "rotationW"))
                writer.uint32(/* id 7, wireType 5 =*/61).float(message.rotationW);
            return writer;
        };

        /**
         * Encodes the specified ClientPlayerPosition message, length delimited. Does not implicitly {@link game.ClientPlayerPosition.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {game.IClientPlayerPosition} message ClientPlayerPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerPosition.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ClientPlayerPosition message from the specified reader or buffer.
         * @function decode
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ClientPlayerPosition} ClientPlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerPosition.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ClientPlayerPosition();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.x = reader.float();
                        break;
                    }
                case 2: {
                        message.y = reader.float();
                        break;
                    }
                case 3: {
                        message.z = reader.float();
                        break;
                    }
                case 4: {
                        message.rotationX = reader.float();
                        break;
                    }
                case 5: {
                        message.rotationY = reader.float();
                        break;
                    }
                case 6: {
                        message.rotationZ = reader.float();
                        break;
                    }
                case 7: {
                        message.rotationW = reader.float();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ClientPlayerPosition message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ClientPlayerPosition} ClientPlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerPosition.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ClientPlayerPosition message.
         * @function verify
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ClientPlayerPosition.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.x != null && message.hasOwnProperty("x"))
                if (typeof message.x !== "number")
                    return "x: number expected";
            if (message.y != null && message.hasOwnProperty("y"))
                if (typeof message.y !== "number")
                    return "y: number expected";
            if (message.z != null && message.hasOwnProperty("z"))
                if (typeof message.z !== "number")
                    return "z: number expected";
            if (message.rotationX != null && message.hasOwnProperty("rotationX"))
                if (typeof message.rotationX !== "number")
                    return "rotationX: number expected";
            if (message.rotationY != null && message.hasOwnProperty("rotationY"))
                if (typeof message.rotationY !== "number")
                    return "rotationY: number expected";
            if (message.rotationZ != null && message.hasOwnProperty("rotationZ"))
                if (typeof message.rotationZ !== "number")
                    return "rotationZ: number expected";
            if (message.rotationW != null && message.hasOwnProperty("rotationW"))
                if (typeof message.rotationW !== "number")
                    return "rotationW: number expected";
            return null;
        };

        /**
         * Creates a ClientPlayerPosition message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ClientPlayerPosition} ClientPlayerPosition
         */
        ClientPlayerPosition.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ClientPlayerPosition)
                return object;
            var message = new $root.game.ClientPlayerPosition();
            if (object.x != null)
                message.x = Number(object.x);
            if (object.y != null)
                message.y = Number(object.y);
            if (object.z != null)
                message.z = Number(object.z);
            if (object.rotationX != null)
                message.rotationX = Number(object.rotationX);
            if (object.rotationY != null)
                message.rotationY = Number(object.rotationY);
            if (object.rotationZ != null)
                message.rotationZ = Number(object.rotationZ);
            if (object.rotationW != null)
                message.rotationW = Number(object.rotationW);
            return message;
        };

        /**
         * Creates a plain object from a ClientPlayerPosition message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {game.ClientPlayerPosition} message ClientPlayerPosition
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ClientPlayerPosition.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.x = 0;
                object.y = 0;
                object.z = 0;
                object.rotationX = 0;
                object.rotationY = 0;
                object.rotationZ = 0;
                object.rotationW = 0;
            }
            if (message.x != null && message.hasOwnProperty("x"))
                object.x = options.json && !isFinite(message.x) ? String(message.x) : message.x;
            if (message.y != null && message.hasOwnProperty("y"))
                object.y = options.json && !isFinite(message.y) ? String(message.y) : message.y;
            if (message.z != null && message.hasOwnProperty("z"))
                object.z = options.json && !isFinite(message.z) ? String(message.z) : message.z;
            if (message.rotationX != null && message.hasOwnProperty("rotationX"))
                object.rotationX = options.json && !isFinite(message.rotationX) ? String(message.rotationX) : message.rotationX;
            if (message.rotationY != null && message.hasOwnProperty("rotationY"))
                object.rotationY = options.json && !isFinite(message.rotationY) ? String(message.rotationY) : message.rotationY;
            if (message.rotationZ != null && message.hasOwnProperty("rotationZ"))
                object.rotationZ = options.json && !isFinite(message.rotationZ) ? String(message.rotationZ) : message.rotationZ;
            if (message.rotationW != null && message.hasOwnProperty("rotationW"))
                object.rotationW = options.json && !isFinite(message.rotationW) ? String(message.rotationW) : message.rotationW;
            return object;
        };

        /**
         * Converts this ClientPlayerPosition to JSON.
         * @function toJSON
         * @memberof game.ClientPlayerPosition
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ClientPlayerPosition.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ClientPlayerPosition
         * @function getTypeUrl
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ClientPlayerPosition.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ClientPlayerPosition";
        };

        return ClientPlayerPosition;
    })();

    game.ClientPlayerAnimation = (function() {

        /**
         * Properties of a ClientPlayerAnimation.
         * @memberof game
         * @interface IClientPlayerAnimation
         * @property {string|null} [curAnimation] ClientPlayerAnimation curAnimation
         * @property {number|null} [speed] ClientPlayerAnimation speed
         * @property {boolean|null} [loop] ClientPlayerAnimation loop
         */

        /**
         * Constructs a new ClientPlayerAnimation.
         * @memberof game
         * @classdesc Represents a ClientPlayerAnimation.
         * @implements IClientPlayerAnimation
         * @constructor
         * @param {game.IClientPlayerAnimation=} [properties] Properties to set
         */
        function ClientPlayerAnimation(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ClientPlayerAnimation curAnimation.
         * @member {string} curAnimation
         * @memberof game.ClientPlayerAnimation
         * @instance
         */
        ClientPlayerAnimation.prototype.curAnimation = "";

        /**
         * ClientPlayerAnimation speed.
         * @member {number} speed
         * @memberof game.ClientPlayerAnimation
         * @instance
         */
        ClientPlayerAnimation.prototype.speed = 0;

        /**
         * ClientPlayerAnimation loop.
         * @member {boolean} loop
         * @memberof game.ClientPlayerAnimation
         * @instance
         */
        ClientPlayerAnimation.prototype.loop = false;

        /**
         * Creates a new ClientPlayerAnimation instance using the specified properties.
         * @function create
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {game.IClientPlayerAnimation=} [properties] Properties to set
         * @returns {game.ClientPlayerAnimation} ClientPlayerAnimation instance
         */
        ClientPlayerAnimation.create = function create(properties) {
            return new ClientPlayerAnimation(properties);
        };

        /**
         * Encodes the specified ClientPlayerAnimation message. Does not implicitly {@link game.ClientPlayerAnimation.verify|verify} messages.
         * @function encode
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {game.IClientPlayerAnimation} message ClientPlayerAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerAnimation.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.curAnimation != null && Object.hasOwnProperty.call(message, "curAnimation"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.curAnimation);
            if (message.speed != null && Object.hasOwnProperty.call(message, "speed"))
                writer.uint32(/* id 2, wireType 5 =*/21).float(message.speed);
            if (message.loop != null && Object.hasOwnProperty.call(message, "loop"))
                writer.uint32(/* id 3, wireType 0 =*/24).bool(message.loop);
            return writer;
        };

        /**
         * Encodes the specified ClientPlayerAnimation message, length delimited. Does not implicitly {@link game.ClientPlayerAnimation.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {game.IClientPlayerAnimation} message ClientPlayerAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerAnimation.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ClientPlayerAnimation message from the specified reader or buffer.
         * @function decode
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ClientPlayerAnimation} ClientPlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerAnimation.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ClientPlayerAnimation();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.curAnimation = reader.string();
                        break;
                    }
                case 2: {
                        message.speed = reader.float();
                        break;
                    }
                case 3: {
                        message.loop = reader.bool();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ClientPlayerAnimation message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ClientPlayerAnimation} ClientPlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerAnimation.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ClientPlayerAnimation message.
         * @function verify
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ClientPlayerAnimation.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.curAnimation != null && message.hasOwnProperty("curAnimation"))
                if (!$util.isString(message.curAnimation))
                    return "curAnimation: string expected";
            if (message.speed != null && message.hasOwnProperty("speed"))
                if (typeof message.speed !== "number")
                    return "speed: number expected";
            if (message.loop != null && message.hasOwnProperty("loop"))
                if (typeof message.loop !== "boolean")
                    return "loop: boolean expected";
            return null;
        };

        /**
         * Creates a ClientPlayerAnimation message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ClientPlayerAnimation} ClientPlayerAnimation
         */
        ClientPlayerAnimation.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ClientPlayerAnimation)
                return object;
            var message = new $root.game.ClientPlayerAnimation();
            if (object.curAnimation != null)
                message.curAnimation = String(object.curAnimation);
            if (object.speed != null)
                message.speed = Number(object.speed);
            if (object.loop != null)
                message.loop = Boolean(object.loop);
            return message;
        };

        /**
         * Creates a plain object from a ClientPlayerAnimation message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {game.ClientPlayerAnimation} message ClientPlayerAnimation
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ClientPlayerAnimation.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.curAnimation = "";
                object.speed = 0;
                object.loop = false;
            }
            if (message.curAnimation != null && message.hasOwnProperty("curAnimation"))
                object.curAnimation = message.curAnimation;
            if (message.speed != null && message.hasOwnProperty("speed"))
                object.speed = options.json && !isFinite(message.speed) ? String(message.speed) : message.speed;
            if (message.loop != null && message.hasOwnProperty("loop"))
                object.loop = message.loop;
            return object;
        };

        /**
         * Converts this ClientPlayerAnimation to JSON.
         * @function toJSON
         * @memberof game.ClientPlayerAnimation
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ClientPlayerAnimation.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ClientPlayerAnimation
         * @function getTypeUrl
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ClientPlayerAnimation.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ClientPlayerAnimation";
        };

        return ClientPlayerAnimation;
    })();

    game.ClientPlayerUpdate = (function() {

        /**
         * Properties of a ClientPlayerUpdate.
         * @memberof game
         * @interface IClientPlayerUpdate
         * @property {string|null} [itemId] ClientPlayerUpdate itemId
         * @property {number|null} [pizzaCount] ClientPlayerUpdate pizzaCount
         * @property {string|null} [petId] ClientPlayerUpdate petId
         */

        /**
         * Constructs a new ClientPlayerUpdate.
         * @memberof game
         * @classdesc Represents a ClientPlayerUpdate.
         * @implements IClientPlayerUpdate
         * @constructor
         * @param {game.IClientPlayerUpdate=} [properties] Properties to set
         */
        function ClientPlayerUpdate(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ClientPlayerUpdate itemId.
         * @member {string} itemId
         * @memberof game.ClientPlayerUpdate
         * @instance
         */
        ClientPlayerUpdate.prototype.itemId = "";

        /**
         * ClientPlayerUpdate pizzaCount.
         * @member {number} pizzaCount
         * @memberof game.ClientPlayerUpdate
         * @instance
         */
        ClientPlayerUpdate.prototype.pizzaCount = 0;

        /**
         * ClientPlayerUpdate petId.
         * @member {string} petId
         * @memberof game.ClientPlayerUpdate
         * @instance
         */
        ClientPlayerUpdate.prototype.petId = "";

        /**
         * Creates a new ClientPlayerUpdate instance using the specified properties.
         * @function create
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {game.IClientPlayerUpdate=} [properties] Properties to set
         * @returns {game.ClientPlayerUpdate} ClientPlayerUpdate instance
         */
        ClientPlayerUpdate.create = function create(properties) {
            return new ClientPlayerUpdate(properties);
        };

        /**
         * Encodes the specified ClientPlayerUpdate message. Does not implicitly {@link game.ClientPlayerUpdate.verify|verify} messages.
         * @function encode
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {game.IClientPlayerUpdate} message ClientPlayerUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerUpdate.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.itemId != null && Object.hasOwnProperty.call(message, "itemId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.itemId);
            if (message.pizzaCount != null && Object.hasOwnProperty.call(message, "pizzaCount"))
                writer.uint32(/* id 2, wireType 0 =*/16).int32(message.pizzaCount);
            if (message.petId != null && Object.hasOwnProperty.call(message, "petId"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.petId);
            return writer;
        };

        /**
         * Encodes the specified ClientPlayerUpdate message, length delimited. Does not implicitly {@link game.ClientPlayerUpdate.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {game.IClientPlayerUpdate} message ClientPlayerUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerUpdate.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ClientPlayerUpdate message from the specified reader or buffer.
         * @function decode
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ClientPlayerUpdate} ClientPlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerUpdate.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ClientPlayerUpdate();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.itemId = reader.string();
                        break;
                    }
                case 2: {
                        message.pizzaCount = reader.int32();
                        break;
                    }
                case 3: {
                        message.petId = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ClientPlayerUpdate message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ClientPlayerUpdate} ClientPlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerUpdate.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ClientPlayerUpdate message.
         * @function verify
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ClientPlayerUpdate.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.itemId != null && message.hasOwnProperty("itemId"))
                if (!$util.isString(message.itemId))
                    return "itemId: string expected";
            if (message.pizzaCount != null && message.hasOwnProperty("pizzaCount"))
                if (!$util.isInteger(message.pizzaCount))
                    return "pizzaCount: integer expected";
            if (message.petId != null && message.hasOwnProperty("petId"))
                if (!$util.isString(message.petId))
                    return "petId: string expected";
            return null;
        };

        /**
         * Creates a ClientPlayerUpdate message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ClientPlayerUpdate} ClientPlayerUpdate
         */
        ClientPlayerUpdate.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ClientPlayerUpdate)
                return object;
            var message = new $root.game.ClientPlayerUpdate();
            if (object.itemId != null)
                message.itemId = String(object.itemId);
            if (object.pizzaCount != null)
                message.pizzaCount = object.pizzaCount | 0;
            if (object.petId != null)
                message.petId = String(object.petId);
            return message;
        };

        /**
         * Creates a plain object from a ClientPlayerUpdate message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {game.ClientPlayerUpdate} message ClientPlayerUpdate
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ClientPlayerUpdate.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.itemId = "";
                object.pizzaCount = 0;
                object.petId = "";
            }
            if (message.itemId != null && message.hasOwnProperty("itemId"))
                object.itemId = message.itemId;
            if (message.pizzaCount != null && message.hasOwnProperty("pizzaCount"))
                object.pizzaCount = message.pizzaCount;
            if (message.petId != null && message.hasOwnProperty("petId"))
                object.petId = message.petId;
            return object;
        };

        /**
         * Converts this ClientPlayerUpdate to JSON.
         * @function toJSON
         * @memberof game.ClientPlayerUpdate
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ClientPlayerUpdate.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ClientPlayerUpdate
         * @function getTypeUrl
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ClientPlayerUpdate.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ClientPlayerUpdate";
        };

        return ClientPlayerUpdate;
    })();

    game.ClientPlayerEnter = (function() {

        /**
         * Properties of a ClientPlayerEnter.
         * @memberof game
         * @interface IClientPlayerEnter
         * @property {game.IAvatarData|null} [avatarData] ClientPlayerEnter avatarData
         */

        /**
         * Constructs a new ClientPlayerEnter.
         * @memberof game
         * @classdesc Represents a ClientPlayerEnter.
         * @implements IClientPlayerEnter
         * @constructor
         * @param {game.IClientPlayerEnter=} [properties] Properties to set
         */
        function ClientPlayerEnter(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ClientPlayerEnter avatarData.
         * @member {game.IAvatarData|null|undefined} avatarData
         * @memberof game.ClientPlayerEnter
         * @instance
         */
        ClientPlayerEnter.prototype.avatarData = null;

        /**
         * Creates a new ClientPlayerEnter instance using the specified properties.
         * @function create
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {game.IClientPlayerEnter=} [properties] Properties to set
         * @returns {game.ClientPlayerEnter} ClientPlayerEnter instance
         */
        ClientPlayerEnter.create = function create(properties) {
            return new ClientPlayerEnter(properties);
        };

        /**
         * Encodes the specified ClientPlayerEnter message. Does not implicitly {@link game.ClientPlayerEnter.verify|verify} messages.
         * @function encode
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {game.IClientPlayerEnter} message ClientPlayerEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerEnter.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.avatarData != null && Object.hasOwnProperty.call(message, "avatarData"))
                $root.game.AvatarData.encode(message.avatarData, writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();
            return writer;
        };

        /**
         * Encodes the specified ClientPlayerEnter message, length delimited. Does not implicitly {@link game.ClientPlayerEnter.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {game.IClientPlayerEnter} message ClientPlayerEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerEnter.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ClientPlayerEnter message from the specified reader or buffer.
         * @function decode
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ClientPlayerEnter} ClientPlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerEnter.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ClientPlayerEnter();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.avatarData = $root.game.AvatarData.decode(reader, reader.uint32());
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ClientPlayerEnter message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ClientPlayerEnter} ClientPlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerEnter.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ClientPlayerEnter message.
         * @function verify
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ClientPlayerEnter.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.avatarData != null && message.hasOwnProperty("avatarData")) {
                var error = $root.game.AvatarData.verify(message.avatarData);
                if (error)
                    return "avatarData." + error;
            }
            return null;
        };

        /**
         * Creates a ClientPlayerEnter message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ClientPlayerEnter} ClientPlayerEnter
         */
        ClientPlayerEnter.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ClientPlayerEnter)
                return object;
            var message = new $root.game.ClientPlayerEnter();
            if (object.avatarData != null) {
                if (typeof object.avatarData !== "object")
                    throw TypeError(".game.ClientPlayerEnter.avatarData: object expected");
                message.avatarData = $root.game.AvatarData.fromObject(object.avatarData);
            }
            return message;
        };

        /**
         * Creates a plain object from a ClientPlayerEnter message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {game.ClientPlayerEnter} message ClientPlayerEnter
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ClientPlayerEnter.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.avatarData = null;
            if (message.avatarData != null && message.hasOwnProperty("avatarData"))
                object.avatarData = $root.game.AvatarData.toObject(message.avatarData, options);
            return object;
        };

        /**
         * Converts this ClientPlayerEnter to JSON.
         * @function toJSON
         * @memberof game.ClientPlayerEnter
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ClientPlayerEnter.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ClientPlayerEnter
         * @function getTypeUrl
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ClientPlayerEnter.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ClientPlayerEnter";
        };

        return ClientPlayerEnter;
    })();

    return game;
})();

module.exports = $root;
