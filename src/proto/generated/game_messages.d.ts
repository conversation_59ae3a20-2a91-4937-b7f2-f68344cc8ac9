import * as $protobuf from "protobufjs";
import Long = require("long");
/** Namespace game. */
export namespace game {

    /** PacketType enum. */
    enum PacketType {
        PLAYER_NONE = 0,
        PLAYER_ENTER = 1,
        PLAYER_LEAVE = 2,
        PLAYER_POSITION = 10,
        PLAYER_ANIMATION = 11,
        PLAYER_UPDATE = 12,
        PET_POSITION = 50,
        PET_ANIMATION = 51,
        CHAT_ENTER = 100,
        CHAT_LEAVE = 101,
        CHAT_MESSAGE = 102,
        RED_PACKET_UPDATE = 201,
        RED_PACKET_REWARD = 202
    }

    /** Properties of a GameMessage. */
    interface IGameMessage {

        /** GameMessage packetType */
        packetType?: (game.PacketType|null);

        /** GameMessage data */
        data?: (Uint8Array|null);

        /** GameMessage timestamp */
        timestamp?: (number|Long|null);
    }

    /** Represents a GameMessage. */
    class GameMessage implements IGameMessage {

        /**
         * Constructs a new GameMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IGameMessage);

        /** GameMessage packetType. */
        public packetType: game.PacketType;

        /** GameMessage data. */
        public data: Uint8Array;

        /** GameMessage timestamp. */
        public timestamp: (number|Long);

        /**
         * Creates a new GameMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns GameMessage instance
         */
        public static create(properties?: game.IGameMessage): game.GameMessage;

        /**
         * Encodes the specified GameMessage message. Does not implicitly {@link game.GameMessage.verify|verify} messages.
         * @param message GameMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IGameMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified GameMessage message, length delimited. Does not implicitly {@link game.GameMessage.verify|verify} messages.
         * @param message GameMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IGameMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a GameMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns GameMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.GameMessage;

        /**
         * Decodes a GameMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns GameMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.GameMessage;

        /**
         * Verifies a GameMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a GameMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns GameMessage
         */
        public static fromObject(object: { [k: string]: any }): game.GameMessage;

        /**
         * Creates a plain object from a GameMessage message. Also converts values to other types if specified.
         * @param message GameMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.GameMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this GameMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for GameMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ClientMessage. */
    interface IClientMessage {

        /** ClientMessage packetType */
        packetType?: (game.PacketType|null);

        /** ClientMessage data */
        data?: (Uint8Array|null);

        /** ClientMessage timestamp */
        timestamp?: (number|Long|null);

        /** ClientMessage btcAddress */
        btcAddress?: (string|null);
    }

    /** Represents a ClientMessage. */
    class ClientMessage implements IClientMessage {

        /**
         * Constructs a new ClientMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IClientMessage);

        /** ClientMessage packetType. */
        public packetType: game.PacketType;

        /** ClientMessage data. */
        public data: Uint8Array;

        /** ClientMessage timestamp. */
        public timestamp: (number|Long);

        /** ClientMessage btcAddress. */
        public btcAddress: string;

        /**
         * Creates a new ClientMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ClientMessage instance
         */
        public static create(properties?: game.IClientMessage): game.ClientMessage;

        /**
         * Encodes the specified ClientMessage message. Does not implicitly {@link game.ClientMessage.verify|verify} messages.
         * @param message ClientMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IClientMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ClientMessage message, length delimited. Does not implicitly {@link game.ClientMessage.verify|verify} messages.
         * @param message ClientMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IClientMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ClientMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ClientMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ClientMessage;

        /**
         * Decodes a ClientMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ClientMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ClientMessage;

        /**
         * Verifies a ClientMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ClientMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ClientMessage
         */
        public static fromObject(object: { [k: string]: any }): game.ClientMessage;

        /**
         * Creates a plain object from a ClientMessage message. Also converts values to other types if specified.
         * @param message ClientMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ClientMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ClientMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ClientMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerPosition. */
    interface IPlayerPosition {

        /** PlayerPosition btcAddress */
        btcAddress?: (string|null);

        /** PlayerPosition x */
        x?: (number|null);

        /** PlayerPosition y */
        y?: (number|null);

        /** PlayerPosition z */
        z?: (number|null);

        /** PlayerPosition rotationX */
        rotationX?: (number|null);

        /** PlayerPosition rotationY */
        rotationY?: (number|null);

        /** PlayerPosition rotationZ */
        rotationZ?: (number|null);

        /** PlayerPosition rotationW */
        rotationW?: (number|null);
    }

    /** Represents a PlayerPosition. */
    class PlayerPosition implements IPlayerPosition {

        /**
         * Constructs a new PlayerPosition.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerPosition);

        /** PlayerPosition btcAddress. */
        public btcAddress: string;

        /** PlayerPosition x. */
        public x: number;

        /** PlayerPosition y. */
        public y: number;

        /** PlayerPosition z. */
        public z: number;

        /** PlayerPosition rotationX. */
        public rotationX: number;

        /** PlayerPosition rotationY. */
        public rotationY: number;

        /** PlayerPosition rotationZ. */
        public rotationZ: number;

        /** PlayerPosition rotationW. */
        public rotationW: number;

        /**
         * Creates a new PlayerPosition instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerPosition instance
         */
        public static create(properties?: game.IPlayerPosition): game.PlayerPosition;

        /**
         * Encodes the specified PlayerPosition message. Does not implicitly {@link game.PlayerPosition.verify|verify} messages.
         * @param message PlayerPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerPosition message, length delimited. Does not implicitly {@link game.PlayerPosition.verify|verify} messages.
         * @param message PlayerPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerPosition message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerPosition;

        /**
         * Decodes a PlayerPosition message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerPosition;

        /**
         * Verifies a PlayerPosition message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerPosition message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerPosition
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerPosition;

        /**
         * Creates a plain object from a PlayerPosition message. Also converts values to other types if specified.
         * @param message PlayerPosition
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerPosition, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerPosition to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerPosition
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerAnimation. */
    interface IPlayerAnimation {

        /** PlayerAnimation btcAddress */
        btcAddress?: (string|null);

        /** PlayerAnimation animationName */
        animationName?: (string|null);

        /** PlayerAnimation speed */
        speed?: (number|null);

        /** PlayerAnimation loop */
        loop?: (boolean|null);
    }

    /** Represents a PlayerAnimation. */
    class PlayerAnimation implements IPlayerAnimation {

        /**
         * Constructs a new PlayerAnimation.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerAnimation);

        /** PlayerAnimation btcAddress. */
        public btcAddress: string;

        /** PlayerAnimation animationName. */
        public animationName: string;

        /** PlayerAnimation speed. */
        public speed: number;

        /** PlayerAnimation loop. */
        public loop: boolean;

        /**
         * Creates a new PlayerAnimation instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerAnimation instance
         */
        public static create(properties?: game.IPlayerAnimation): game.PlayerAnimation;

        /**
         * Encodes the specified PlayerAnimation message. Does not implicitly {@link game.PlayerAnimation.verify|verify} messages.
         * @param message PlayerAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerAnimation message, length delimited. Does not implicitly {@link game.PlayerAnimation.verify|verify} messages.
         * @param message PlayerAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerAnimation message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerAnimation;

        /**
         * Decodes a PlayerAnimation message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerAnimation;

        /**
         * Verifies a PlayerAnimation message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerAnimation message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerAnimation
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerAnimation;

        /**
         * Creates a plain object from a PlayerAnimation message. Also converts values to other types if specified.
         * @param message PlayerAnimation
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerAnimation, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerAnimation to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerAnimation
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerUpdate. */
    interface IPlayerUpdate {

        /** PlayerUpdate btcAddress */
        btcAddress?: (string|null);

        /** PlayerUpdate key */
        key?: (string|null);

        /** PlayerUpdate value */
        value?: (string|null);
    }

    /** Represents a PlayerUpdate. */
    class PlayerUpdate implements IPlayerUpdate {

        /**
         * Constructs a new PlayerUpdate.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerUpdate);

        /** PlayerUpdate btcAddress. */
        public btcAddress: string;

        /** PlayerUpdate key. */
        public key: string;

        /** PlayerUpdate value. */
        public value: string;

        /**
         * Creates a new PlayerUpdate instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerUpdate instance
         */
        public static create(properties?: game.IPlayerUpdate): game.PlayerUpdate;

        /**
         * Encodes the specified PlayerUpdate message. Does not implicitly {@link game.PlayerUpdate.verify|verify} messages.
         * @param message PlayerUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerUpdate message, length delimited. Does not implicitly {@link game.PlayerUpdate.verify|verify} messages.
         * @param message PlayerUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerUpdate message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerUpdate;

        /**
         * Decodes a PlayerUpdate message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerUpdate;

        /**
         * Verifies a PlayerUpdate message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerUpdate message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerUpdate
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerUpdate;

        /**
         * Creates a plain object from a PlayerUpdate message. Also converts values to other types if specified.
         * @param message PlayerUpdate
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerUpdate, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerUpdate to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerUpdate
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerEnter. */
    interface IPlayerEnter {

        /** PlayerEnter btcAddress */
        btcAddress?: (string|null);

        /** PlayerEnter sessionId */
        sessionId?: (string|null);

        /** PlayerEnter avatarData */
        avatarData?: (game.IAvatarData|null);

        /** PlayerEnter position */
        position?: (game.IPlayerPosition|null);
    }

    /** Represents a PlayerEnter. */
    class PlayerEnter implements IPlayerEnter {

        /**
         * Constructs a new PlayerEnter.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerEnter);

        /** PlayerEnter btcAddress. */
        public btcAddress: string;

        /** PlayerEnter sessionId. */
        public sessionId: string;

        /** PlayerEnter avatarData. */
        public avatarData?: (game.IAvatarData|null);

        /** PlayerEnter position. */
        public position?: (game.IPlayerPosition|null);

        /**
         * Creates a new PlayerEnter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerEnter instance
         */
        public static create(properties?: game.IPlayerEnter): game.PlayerEnter;

        /**
         * Encodes the specified PlayerEnter message. Does not implicitly {@link game.PlayerEnter.verify|verify} messages.
         * @param message PlayerEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerEnter message, length delimited. Does not implicitly {@link game.PlayerEnter.verify|verify} messages.
         * @param message PlayerEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerEnter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerEnter;

        /**
         * Decodes a PlayerEnter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerEnter;

        /**
         * Verifies a PlayerEnter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerEnter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerEnter
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerEnter;

        /**
         * Creates a plain object from a PlayerEnter message. Also converts values to other types if specified.
         * @param message PlayerEnter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerEnter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerEnter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerEnter
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerLeave. */
    interface IPlayerLeave {

        /** PlayerLeave btcAddress */
        btcAddress?: (string|null);
    }

    /** Represents a PlayerLeave. */
    class PlayerLeave implements IPlayerLeave {

        /**
         * Constructs a new PlayerLeave.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerLeave);

        /** PlayerLeave btcAddress. */
        public btcAddress: string;

        /**
         * Creates a new PlayerLeave instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerLeave instance
         */
        public static create(properties?: game.IPlayerLeave): game.PlayerLeave;

        /**
         * Encodes the specified PlayerLeave message. Does not implicitly {@link game.PlayerLeave.verify|verify} messages.
         * @param message PlayerLeave message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerLeave, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerLeave message, length delimited. Does not implicitly {@link game.PlayerLeave.verify|verify} messages.
         * @param message PlayerLeave message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerLeave, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerLeave message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerLeave;

        /**
         * Decodes a PlayerLeave message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerLeave;

        /**
         * Verifies a PlayerLeave message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerLeave message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerLeave
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerLeave;

        /**
         * Creates a plain object from a PlayerLeave message. Also converts values to other types if specified.
         * @param message PlayerLeave
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerLeave, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerLeave to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerLeave
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an AvatarData. */
    interface IAvatarData {

        /** AvatarData shirtId */
        shirtId?: (string|null);

        /** AvatarData shirtTextureId */
        shirtTextureId?: (string|null);

        /** AvatarData shirtColor */
        shirtColor?: (string|null);

        /** AvatarData pantsId */
        pantsId?: (string|null);

        /** AvatarData shoesId */
        shoesId?: (string|null);

        /** AvatarData hatId */
        hatId?: (string|null);
    }

    /** Represents an AvatarData. */
    class AvatarData implements IAvatarData {

        /**
         * Constructs a new AvatarData.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IAvatarData);

        /** AvatarData shirtId. */
        public shirtId: string;

        /** AvatarData shirtTextureId. */
        public shirtTextureId: string;

        /** AvatarData shirtColor. */
        public shirtColor: string;

        /** AvatarData pantsId. */
        public pantsId: string;

        /** AvatarData shoesId. */
        public shoesId: string;

        /** AvatarData hatId. */
        public hatId: string;

        /**
         * Creates a new AvatarData instance using the specified properties.
         * @param [properties] Properties to set
         * @returns AvatarData instance
         */
        public static create(properties?: game.IAvatarData): game.AvatarData;

        /**
         * Encodes the specified AvatarData message. Does not implicitly {@link game.AvatarData.verify|verify} messages.
         * @param message AvatarData message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IAvatarData, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified AvatarData message, length delimited. Does not implicitly {@link game.AvatarData.verify|verify} messages.
         * @param message AvatarData message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IAvatarData, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an AvatarData message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns AvatarData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.AvatarData;

        /**
         * Decodes an AvatarData message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns AvatarData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.AvatarData;

        /**
         * Verifies an AvatarData message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an AvatarData message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns AvatarData
         */
        public static fromObject(object: { [k: string]: any }): game.AvatarData;

        /**
         * Creates a plain object from an AvatarData message. Also converts values to other types if specified.
         * @param message AvatarData
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.AvatarData, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this AvatarData to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for AvatarData
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PetPosition. */
    interface IPetPosition {

        /** PetPosition ownerBtcAddress */
        ownerBtcAddress?: (string|null);

        /** PetPosition petId */
        petId?: (string|null);

        /** PetPosition x */
        x?: (number|null);

        /** PetPosition y */
        y?: (number|null);

        /** PetPosition z */
        z?: (number|null);

        /** PetPosition rotationX */
        rotationX?: (number|null);

        /** PetPosition rotationY */
        rotationY?: (number|null);

        /** PetPosition rotationZ */
        rotationZ?: (number|null);

        /** PetPosition rotationW */
        rotationW?: (number|null);
    }

    /** Represents a PetPosition. */
    class PetPosition implements IPetPosition {

        /**
         * Constructs a new PetPosition.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPetPosition);

        /** PetPosition ownerBtcAddress. */
        public ownerBtcAddress: string;

        /** PetPosition petId. */
        public petId: string;

        /** PetPosition x. */
        public x: number;

        /** PetPosition y. */
        public y: number;

        /** PetPosition z. */
        public z: number;

        /** PetPosition rotationX. */
        public rotationX: number;

        /** PetPosition rotationY. */
        public rotationY: number;

        /** PetPosition rotationZ. */
        public rotationZ: number;

        /** PetPosition rotationW. */
        public rotationW: number;

        /**
         * Creates a new PetPosition instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PetPosition instance
         */
        public static create(properties?: game.IPetPosition): game.PetPosition;

        /**
         * Encodes the specified PetPosition message. Does not implicitly {@link game.PetPosition.verify|verify} messages.
         * @param message PetPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPetPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PetPosition message, length delimited. Does not implicitly {@link game.PetPosition.verify|verify} messages.
         * @param message PetPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPetPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PetPosition message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PetPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PetPosition;

        /**
         * Decodes a PetPosition message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PetPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PetPosition;

        /**
         * Verifies a PetPosition message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PetPosition message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PetPosition
         */
        public static fromObject(object: { [k: string]: any }): game.PetPosition;

        /**
         * Creates a plain object from a PetPosition message. Also converts values to other types if specified.
         * @param message PetPosition
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PetPosition, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PetPosition to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PetPosition
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PetAnimation. */
    interface IPetAnimation {

        /** PetAnimation ownerBtcAddress */
        ownerBtcAddress?: (string|null);

        /** PetAnimation petId */
        petId?: (string|null);

        /** PetAnimation animationName */
        animationName?: (string|null);
    }

    /** Represents a PetAnimation. */
    class PetAnimation implements IPetAnimation {

        /**
         * Constructs a new PetAnimation.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPetAnimation);

        /** PetAnimation ownerBtcAddress. */
        public ownerBtcAddress: string;

        /** PetAnimation petId. */
        public petId: string;

        /** PetAnimation animationName. */
        public animationName: string;

        /**
         * Creates a new PetAnimation instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PetAnimation instance
         */
        public static create(properties?: game.IPetAnimation): game.PetAnimation;

        /**
         * Encodes the specified PetAnimation message. Does not implicitly {@link game.PetAnimation.verify|verify} messages.
         * @param message PetAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPetAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PetAnimation message, length delimited. Does not implicitly {@link game.PetAnimation.verify|verify} messages.
         * @param message PetAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPetAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PetAnimation message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PetAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PetAnimation;

        /**
         * Decodes a PetAnimation message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PetAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PetAnimation;

        /**
         * Verifies a PetAnimation message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PetAnimation message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PetAnimation
         */
        public static fromObject(object: { [k: string]: any }): game.PetAnimation;

        /**
         * Creates a plain object from a PetAnimation message. Also converts values to other types if specified.
         * @param message PetAnimation
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PetAnimation, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PetAnimation to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PetAnimation
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ChatEnter. */
    interface IChatEnter {

        /** ChatEnter chatId */
        chatId?: (number|null);

        /** ChatEnter btcAddress */
        btcAddress?: (string|null);
    }

    /** Represents a ChatEnter. */
    class ChatEnter implements IChatEnter {

        /**
         * Constructs a new ChatEnter.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IChatEnter);

        /** ChatEnter chatId. */
        public chatId: number;

        /** ChatEnter btcAddress. */
        public btcAddress: string;

        /**
         * Creates a new ChatEnter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ChatEnter instance
         */
        public static create(properties?: game.IChatEnter): game.ChatEnter;

        /**
         * Encodes the specified ChatEnter message. Does not implicitly {@link game.ChatEnter.verify|verify} messages.
         * @param message ChatEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IChatEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ChatEnter message, length delimited. Does not implicitly {@link game.ChatEnter.verify|verify} messages.
         * @param message ChatEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IChatEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ChatEnter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ChatEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ChatEnter;

        /**
         * Decodes a ChatEnter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ChatEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ChatEnter;

        /**
         * Verifies a ChatEnter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ChatEnter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ChatEnter
         */
        public static fromObject(object: { [k: string]: any }): game.ChatEnter;

        /**
         * Creates a plain object from a ChatEnter message. Also converts values to other types if specified.
         * @param message ChatEnter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ChatEnter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ChatEnter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ChatEnter
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ChatLeave. */
    interface IChatLeave {

        /** ChatLeave chatId */
        chatId?: (number|null);

        /** ChatLeave btcAddress */
        btcAddress?: (string|null);
    }

    /** Represents a ChatLeave. */
    class ChatLeave implements IChatLeave {

        /**
         * Constructs a new ChatLeave.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IChatLeave);

        /** ChatLeave chatId. */
        public chatId: number;

        /** ChatLeave btcAddress. */
        public btcAddress: string;

        /**
         * Creates a new ChatLeave instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ChatLeave instance
         */
        public static create(properties?: game.IChatLeave): game.ChatLeave;

        /**
         * Encodes the specified ChatLeave message. Does not implicitly {@link game.ChatLeave.verify|verify} messages.
         * @param message ChatLeave message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IChatLeave, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ChatLeave message, length delimited. Does not implicitly {@link game.ChatLeave.verify|verify} messages.
         * @param message ChatLeave message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IChatLeave, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ChatLeave message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ChatLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ChatLeave;

        /**
         * Decodes a ChatLeave message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ChatLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ChatLeave;

        /**
         * Verifies a ChatLeave message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ChatLeave message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ChatLeave
         */
        public static fromObject(object: { [k: string]: any }): game.ChatLeave;

        /**
         * Creates a plain object from a ChatLeave message. Also converts values to other types if specified.
         * @param message ChatLeave
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ChatLeave, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ChatLeave to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ChatLeave
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ChatMessage. */
    interface IChatMessage {

        /** ChatMessage uuid */
        uuid?: (string|null);

        /** ChatMessage playerId */
        playerId?: (string|null);

        /** ChatMessage content */
        content?: (string|null);

        /** ChatMessage replyTo */
        replyTo?: (string|null);

        /** ChatMessage timestamp */
        timestamp?: (number|Long|null);

        /** ChatMessage tabType */
        tabType?: (number|null);
    }

    /** Represents a ChatMessage. */
    class ChatMessage implements IChatMessage {

        /**
         * Constructs a new ChatMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IChatMessage);

        /** ChatMessage uuid. */
        public uuid: string;

        /** ChatMessage playerId. */
        public playerId: string;

        /** ChatMessage content. */
        public content: string;

        /** ChatMessage replyTo. */
        public replyTo: string;

        /** ChatMessage timestamp. */
        public timestamp: (number|Long);

        /** ChatMessage tabType. */
        public tabType: number;

        /**
         * Creates a new ChatMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ChatMessage instance
         */
        public static create(properties?: game.IChatMessage): game.ChatMessage;

        /**
         * Encodes the specified ChatMessage message. Does not implicitly {@link game.ChatMessage.verify|verify} messages.
         * @param message ChatMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IChatMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ChatMessage message, length delimited. Does not implicitly {@link game.ChatMessage.verify|verify} messages.
         * @param message ChatMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IChatMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ChatMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ChatMessage;

        /**
         * Decodes a ChatMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ChatMessage;

        /**
         * Verifies a ChatMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ChatMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ChatMessage
         */
        public static fromObject(object: { [k: string]: any }): game.ChatMessage;

        /**
         * Creates a plain object from a ChatMessage message. Also converts values to other types if specified.
         * @param message ChatMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ChatMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ChatMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ChatMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RedPacketUpdate. */
    interface IRedPacketUpdate {

        /** RedPacketUpdate packetId */
        packetId?: (string|null);

        /** RedPacketUpdate creatorAddress */
        creatorAddress?: (string|null);

        /** RedPacketUpdate amount */
        amount?: (number|null);

        /** RedPacketUpdate count */
        count?: (number|null);

        /** RedPacketUpdate remaining */
        remaining?: (number|null);

        /** RedPacketUpdate status */
        status?: (string|null);

        /** RedPacketUpdate createdAt */
        createdAt?: (number|Long|null);

        /** RedPacketUpdate expiresAt */
        expiresAt?: (number|Long|null);
    }

    /** Represents a RedPacketUpdate. */
    class RedPacketUpdate implements IRedPacketUpdate {

        /**
         * Constructs a new RedPacketUpdate.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IRedPacketUpdate);

        /** RedPacketUpdate packetId. */
        public packetId: string;

        /** RedPacketUpdate creatorAddress. */
        public creatorAddress: string;

        /** RedPacketUpdate amount. */
        public amount: number;

        /** RedPacketUpdate count. */
        public count: number;

        /** RedPacketUpdate remaining. */
        public remaining: number;

        /** RedPacketUpdate status. */
        public status: string;

        /** RedPacketUpdate createdAt. */
        public createdAt: (number|Long);

        /** RedPacketUpdate expiresAt. */
        public expiresAt: (number|Long);

        /**
         * Creates a new RedPacketUpdate instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RedPacketUpdate instance
         */
        public static create(properties?: game.IRedPacketUpdate): game.RedPacketUpdate;

        /**
         * Encodes the specified RedPacketUpdate message. Does not implicitly {@link game.RedPacketUpdate.verify|verify} messages.
         * @param message RedPacketUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IRedPacketUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RedPacketUpdate message, length delimited. Does not implicitly {@link game.RedPacketUpdate.verify|verify} messages.
         * @param message RedPacketUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IRedPacketUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RedPacketUpdate message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RedPacketUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.RedPacketUpdate;

        /**
         * Decodes a RedPacketUpdate message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RedPacketUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.RedPacketUpdate;

        /**
         * Verifies a RedPacketUpdate message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RedPacketUpdate message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RedPacketUpdate
         */
        public static fromObject(object: { [k: string]: any }): game.RedPacketUpdate;

        /**
         * Creates a plain object from a RedPacketUpdate message. Also converts values to other types if specified.
         * @param message RedPacketUpdate
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.RedPacketUpdate, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RedPacketUpdate to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RedPacketUpdate
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RedPacketReward. */
    interface IRedPacketReward {

        /** RedPacketReward packetId */
        packetId?: (string|null);

        /** RedPacketReward receiverAddress */
        receiverAddress?: (string|null);

        /** RedPacketReward amount */
        amount?: (number|null);

        /** RedPacketReward receivedAt */
        receivedAt?: (number|Long|null);
    }

    /** Represents a RedPacketReward. */
    class RedPacketReward implements IRedPacketReward {

        /**
         * Constructs a new RedPacketReward.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IRedPacketReward);

        /** RedPacketReward packetId. */
        public packetId: string;

        /** RedPacketReward receiverAddress. */
        public receiverAddress: string;

        /** RedPacketReward amount. */
        public amount: number;

        /** RedPacketReward receivedAt. */
        public receivedAt: (number|Long);

        /**
         * Creates a new RedPacketReward instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RedPacketReward instance
         */
        public static create(properties?: game.IRedPacketReward): game.RedPacketReward;

        /**
         * Encodes the specified RedPacketReward message. Does not implicitly {@link game.RedPacketReward.verify|verify} messages.
         * @param message RedPacketReward message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IRedPacketReward, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RedPacketReward message, length delimited. Does not implicitly {@link game.RedPacketReward.verify|verify} messages.
         * @param message RedPacketReward message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IRedPacketReward, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RedPacketReward message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RedPacketReward
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.RedPacketReward;

        /**
         * Decodes a RedPacketReward message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RedPacketReward
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.RedPacketReward;

        /**
         * Verifies a RedPacketReward message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RedPacketReward message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RedPacketReward
         */
        public static fromObject(object: { [k: string]: any }): game.RedPacketReward;

        /**
         * Creates a plain object from a RedPacketReward message. Also converts values to other types if specified.
         * @param message RedPacketReward
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.RedPacketReward, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RedPacketReward to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RedPacketReward
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerActionMessage. */
    interface IPlayerActionMessage {

        /** PlayerActionMessage pid */
        pid?: (number|null);

        /** PlayerActionMessage actionData */
        actionData?: (Uint8Array|null);

        /** PlayerActionMessage btcAddress */
        btcAddress?: (string|null);

        /** PlayerActionMessage timestamp */
        timestamp?: (number|Long|null);
    }

    /** Represents a PlayerActionMessage. */
    class PlayerActionMessage implements IPlayerActionMessage {

        /**
         * Constructs a new PlayerActionMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerActionMessage);

        /** PlayerActionMessage pid. */
        public pid: number;

        /** PlayerActionMessage actionData. */
        public actionData: Uint8Array;

        /** PlayerActionMessage btcAddress. */
        public btcAddress: string;

        /** PlayerActionMessage timestamp. */
        public timestamp: (number|Long);

        /**
         * Creates a new PlayerActionMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerActionMessage instance
         */
        public static create(properties?: game.IPlayerActionMessage): game.PlayerActionMessage;

        /**
         * Encodes the specified PlayerActionMessage message. Does not implicitly {@link game.PlayerActionMessage.verify|verify} messages.
         * @param message PlayerActionMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerActionMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerActionMessage message, length delimited. Does not implicitly {@link game.PlayerActionMessage.verify|verify} messages.
         * @param message PlayerActionMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerActionMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerActionMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerActionMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerActionMessage;

        /**
         * Decodes a PlayerActionMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerActionMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerActionMessage;

        /**
         * Verifies a PlayerActionMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerActionMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerActionMessage
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerActionMessage;

        /**
         * Creates a plain object from a PlayerActionMessage message. Also converts values to other types if specified.
         * @param message PlayerActionMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerActionMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerActionMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerActionMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerRequestMessage. */
    interface IPlayerRequestMessage {

        /** PlayerRequestMessage pid */
        pid?: (number|null);

        /** PlayerRequestMessage requestData */
        requestData?: (Uint8Array|null);

        /** PlayerRequestMessage btcAddress */
        btcAddress?: (string|null);

        /** PlayerRequestMessage timestamp */
        timestamp?: (number|Long|null);
    }

    /** Represents a PlayerRequestMessage. */
    class PlayerRequestMessage implements IPlayerRequestMessage {

        /**
         * Constructs a new PlayerRequestMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerRequestMessage);

        /** PlayerRequestMessage pid. */
        public pid: number;

        /** PlayerRequestMessage requestData. */
        public requestData: Uint8Array;

        /** PlayerRequestMessage btcAddress. */
        public btcAddress: string;

        /** PlayerRequestMessage timestamp. */
        public timestamp: (number|Long);

        /**
         * Creates a new PlayerRequestMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerRequestMessage instance
         */
        public static create(properties?: game.IPlayerRequestMessage): game.PlayerRequestMessage;

        /**
         * Encodes the specified PlayerRequestMessage message. Does not implicitly {@link game.PlayerRequestMessage.verify|verify} messages.
         * @param message PlayerRequestMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerRequestMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerRequestMessage message, length delimited. Does not implicitly {@link game.PlayerRequestMessage.verify|verify} messages.
         * @param message PlayerRequestMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerRequestMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerRequestMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerRequestMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerRequestMessage;

        /**
         * Decodes a PlayerRequestMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerRequestMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerRequestMessage;

        /**
         * Verifies a PlayerRequestMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerRequestMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerRequestMessage
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerRequestMessage;

        /**
         * Creates a plain object from a PlayerRequestMessage message. Also converts values to other types if specified.
         * @param message PlayerRequestMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerRequestMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerRequestMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerRequestMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerChatMessage. */
    interface IPlayerChatMessage {

        /** PlayerChatMessage chatId */
        chatId?: (number|null);

        /** PlayerChatMessage pid */
        pid?: (number|null);

        /** PlayerChatMessage chatData */
        chatData?: (Uint8Array|null);

        /** PlayerChatMessage btcAddress */
        btcAddress?: (string|null);

        /** PlayerChatMessage timestamp */
        timestamp?: (number|Long|null);
    }

    /** Represents a PlayerChatMessage. */
    class PlayerChatMessage implements IPlayerChatMessage {

        /**
         * Constructs a new PlayerChatMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerChatMessage);

        /** PlayerChatMessage chatId. */
        public chatId: number;

        /** PlayerChatMessage pid. */
        public pid: number;

        /** PlayerChatMessage chatData. */
        public chatData: Uint8Array;

        /** PlayerChatMessage btcAddress. */
        public btcAddress: string;

        /** PlayerChatMessage timestamp. */
        public timestamp: (number|Long);

        /**
         * Creates a new PlayerChatMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerChatMessage instance
         */
        public static create(properties?: game.IPlayerChatMessage): game.PlayerChatMessage;

        /**
         * Encodes the specified PlayerChatMessage message. Does not implicitly {@link game.PlayerChatMessage.verify|verify} messages.
         * @param message PlayerChatMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerChatMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerChatMessage message, length delimited. Does not implicitly {@link game.PlayerChatMessage.verify|verify} messages.
         * @param message PlayerChatMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerChatMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerChatMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerChatMessage;

        /**
         * Decodes a PlayerChatMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerChatMessage;

        /**
         * Verifies a PlayerChatMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerChatMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerChatMessage
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerChatMessage;

        /**
         * Creates a plain object from a PlayerChatMessage message. Also converts values to other types if specified.
         * @param message PlayerChatMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerChatMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerChatMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerChatMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ClientPlayerPosition. */
    interface IClientPlayerPosition {

        /** ClientPlayerPosition x */
        x?: (number|null);

        /** ClientPlayerPosition y */
        y?: (number|null);

        /** ClientPlayerPosition z */
        z?: (number|null);

        /** ClientPlayerPosition rotationX */
        rotationX?: (number|null);

        /** ClientPlayerPosition rotationY */
        rotationY?: (number|null);

        /** ClientPlayerPosition rotationZ */
        rotationZ?: (number|null);

        /** ClientPlayerPosition rotationW */
        rotationW?: (number|null);
    }

    /** Represents a ClientPlayerPosition. */
    class ClientPlayerPosition implements IClientPlayerPosition {

        /**
         * Constructs a new ClientPlayerPosition.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IClientPlayerPosition);

        /** ClientPlayerPosition x. */
        public x: number;

        /** ClientPlayerPosition y. */
        public y: number;

        /** ClientPlayerPosition z. */
        public z: number;

        /** ClientPlayerPosition rotationX. */
        public rotationX: number;

        /** ClientPlayerPosition rotationY. */
        public rotationY: number;

        /** ClientPlayerPosition rotationZ. */
        public rotationZ: number;

        /** ClientPlayerPosition rotationW. */
        public rotationW: number;

        /**
         * Creates a new ClientPlayerPosition instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ClientPlayerPosition instance
         */
        public static create(properties?: game.IClientPlayerPosition): game.ClientPlayerPosition;

        /**
         * Encodes the specified ClientPlayerPosition message. Does not implicitly {@link game.ClientPlayerPosition.verify|verify} messages.
         * @param message ClientPlayerPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IClientPlayerPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ClientPlayerPosition message, length delimited. Does not implicitly {@link game.ClientPlayerPosition.verify|verify} messages.
         * @param message ClientPlayerPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IClientPlayerPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ClientPlayerPosition message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ClientPlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ClientPlayerPosition;

        /**
         * Decodes a ClientPlayerPosition message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ClientPlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ClientPlayerPosition;

        /**
         * Verifies a ClientPlayerPosition message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ClientPlayerPosition message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ClientPlayerPosition
         */
        public static fromObject(object: { [k: string]: any }): game.ClientPlayerPosition;

        /**
         * Creates a plain object from a ClientPlayerPosition message. Also converts values to other types if specified.
         * @param message ClientPlayerPosition
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ClientPlayerPosition, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ClientPlayerPosition to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ClientPlayerPosition
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ClientPlayerAnimation. */
    interface IClientPlayerAnimation {

        /** ClientPlayerAnimation curAnimation */
        curAnimation?: (string|null);

        /** ClientPlayerAnimation speed */
        speed?: (number|null);

        /** ClientPlayerAnimation loop */
        loop?: (boolean|null);
    }

    /** Represents a ClientPlayerAnimation. */
    class ClientPlayerAnimation implements IClientPlayerAnimation {

        /**
         * Constructs a new ClientPlayerAnimation.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IClientPlayerAnimation);

        /** ClientPlayerAnimation curAnimation. */
        public curAnimation: string;

        /** ClientPlayerAnimation speed. */
        public speed: number;

        /** ClientPlayerAnimation loop. */
        public loop: boolean;

        /**
         * Creates a new ClientPlayerAnimation instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ClientPlayerAnimation instance
         */
        public static create(properties?: game.IClientPlayerAnimation): game.ClientPlayerAnimation;

        /**
         * Encodes the specified ClientPlayerAnimation message. Does not implicitly {@link game.ClientPlayerAnimation.verify|verify} messages.
         * @param message ClientPlayerAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IClientPlayerAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ClientPlayerAnimation message, length delimited. Does not implicitly {@link game.ClientPlayerAnimation.verify|verify} messages.
         * @param message ClientPlayerAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IClientPlayerAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ClientPlayerAnimation message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ClientPlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ClientPlayerAnimation;

        /**
         * Decodes a ClientPlayerAnimation message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ClientPlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ClientPlayerAnimation;

        /**
         * Verifies a ClientPlayerAnimation message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ClientPlayerAnimation message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ClientPlayerAnimation
         */
        public static fromObject(object: { [k: string]: any }): game.ClientPlayerAnimation;

        /**
         * Creates a plain object from a ClientPlayerAnimation message. Also converts values to other types if specified.
         * @param message ClientPlayerAnimation
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ClientPlayerAnimation, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ClientPlayerAnimation to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ClientPlayerAnimation
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ClientPlayerUpdate. */
    interface IClientPlayerUpdate {

        /** ClientPlayerUpdate itemId */
        itemId?: (string|null);

        /** ClientPlayerUpdate pizzaCount */
        pizzaCount?: (number|null);

        /** ClientPlayerUpdate petId */
        petId?: (string|null);
    }

    /** Represents a ClientPlayerUpdate. */
    class ClientPlayerUpdate implements IClientPlayerUpdate {

        /**
         * Constructs a new ClientPlayerUpdate.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IClientPlayerUpdate);

        /** ClientPlayerUpdate itemId. */
        public itemId: string;

        /** ClientPlayerUpdate pizzaCount. */
        public pizzaCount: number;

        /** ClientPlayerUpdate petId. */
        public petId: string;

        /**
         * Creates a new ClientPlayerUpdate instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ClientPlayerUpdate instance
         */
        public static create(properties?: game.IClientPlayerUpdate): game.ClientPlayerUpdate;

        /**
         * Encodes the specified ClientPlayerUpdate message. Does not implicitly {@link game.ClientPlayerUpdate.verify|verify} messages.
         * @param message ClientPlayerUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IClientPlayerUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ClientPlayerUpdate message, length delimited. Does not implicitly {@link game.ClientPlayerUpdate.verify|verify} messages.
         * @param message ClientPlayerUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IClientPlayerUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ClientPlayerUpdate message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ClientPlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ClientPlayerUpdate;

        /**
         * Decodes a ClientPlayerUpdate message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ClientPlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ClientPlayerUpdate;

        /**
         * Verifies a ClientPlayerUpdate message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ClientPlayerUpdate message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ClientPlayerUpdate
         */
        public static fromObject(object: { [k: string]: any }): game.ClientPlayerUpdate;

        /**
         * Creates a plain object from a ClientPlayerUpdate message. Also converts values to other types if specified.
         * @param message ClientPlayerUpdate
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ClientPlayerUpdate, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ClientPlayerUpdate to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ClientPlayerUpdate
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ClientPlayerEnter. */
    interface IClientPlayerEnter {

        /** ClientPlayerEnter avatarData */
        avatarData?: (game.IAvatarData|null);
    }

    /** Represents a ClientPlayerEnter. */
    class ClientPlayerEnter implements IClientPlayerEnter {

        /**
         * Constructs a new ClientPlayerEnter.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IClientPlayerEnter);

        /** ClientPlayerEnter avatarData. */
        public avatarData?: (game.IAvatarData|null);

        /**
         * Creates a new ClientPlayerEnter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ClientPlayerEnter instance
         */
        public static create(properties?: game.IClientPlayerEnter): game.ClientPlayerEnter;

        /**
         * Encodes the specified ClientPlayerEnter message. Does not implicitly {@link game.ClientPlayerEnter.verify|verify} messages.
         * @param message ClientPlayerEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IClientPlayerEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ClientPlayerEnter message, length delimited. Does not implicitly {@link game.ClientPlayerEnter.verify|verify} messages.
         * @param message ClientPlayerEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IClientPlayerEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ClientPlayerEnter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ClientPlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ClientPlayerEnter;

        /**
         * Decodes a ClientPlayerEnter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ClientPlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ClientPlayerEnter;

        /**
         * Verifies a ClientPlayerEnter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ClientPlayerEnter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ClientPlayerEnter
         */
        public static fromObject(object: { [k: string]: any }): game.ClientPlayerEnter;

        /**
         * Creates a plain object from a ClientPlayerEnter message. Also converts values to other types if specified.
         * @param message ClientPlayerEnter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ClientPlayerEnter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ClientPlayerEnter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ClientPlayerEnter
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }
}
