import * as $protobuf from "protobufjs";
import Long = require("long");
/** Namespace game. */
export namespace game {

    /** PacketType enum. */
    enum PacketType {
        PLAYER_ENTER = 1,
        PLAYER_LEAVE = 2,
        PLAYER_POSITION = 10,
        PLAYER_ANIMATION = 11,
        PLAYER_UPDATE = 12,
        PET_POSITION = 50,
        PET_ANIMATION = 51,
        CHAT_ENTER = 100,
        CHAT_LEAVE = 101,
        CHAT_MESSAGE = 102,
        RED_PACKET_UPDATE = 201,
        RED_PACKET_REWARD = 202
    }

    /** Properties of a GameMessage. */
    interface IGameMessage {

        /** GameMessage packetType */
        packetType?: (game.PacketType|null);

        /** GameMessage data */
        data?: (Uint8Array|null);

        /** GameMessage timestamp */
        timestamp?: (number|Long|null);
    }

    /** Represents a GameMessage. */
    class GameMessage implements IGameMessage {

        /**
         * Constructs a new GameMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IGameMessage);

        /** GameMessage packetType. */
        public packetType: game.PacketType;

        /** GameMessage data. */
        public data: Uint8Array;

        /** GameMessage timestamp. */
        public timestamp: (number|Long);

        /**
         * Creates a new GameMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns GameMessage instance
         */
        public static create(properties?: game.IGameMessage): game.GameMessage;

        /**
         * Encodes the specified GameMessage message. Does not implicitly {@link game.GameMessage.verify|verify} messages.
         * @param message GameMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IGameMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified GameMessage message, length delimited. Does not implicitly {@link game.GameMessage.verify|verify} messages.
         * @param message GameMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IGameMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a GameMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns GameMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.GameMessage;

        /**
         * Decodes a GameMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns GameMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.GameMessage;

        /**
         * Verifies a GameMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a GameMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns GameMessage
         */
        public static fromObject(object: { [k: string]: any }): game.GameMessage;

        /**
         * Creates a plain object from a GameMessage message. Also converts values to other types if specified.
         * @param message GameMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.GameMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this GameMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for GameMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerPosition. */
    interface IPlayerPosition {

        /** PlayerPosition btcAddress */
        btcAddress?: (string|null);

        /** PlayerPosition x */
        x?: (number|null);

        /** PlayerPosition y */
        y?: (number|null);

        /** PlayerPosition z */
        z?: (number|null);

        /** PlayerPosition rotationX */
        rotationX?: (number|null);

        /** PlayerPosition rotationY */
        rotationY?: (number|null);

        /** PlayerPosition rotationZ */
        rotationZ?: (number|null);

        /** PlayerPosition rotationW */
        rotationW?: (number|null);
    }

    /** Represents a PlayerPosition. */
    class PlayerPosition implements IPlayerPosition {

        /**
         * Constructs a new PlayerPosition.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerPosition);

        /** PlayerPosition btcAddress. */
        public btcAddress: string;

        /** PlayerPosition x. */
        public x: number;

        /** PlayerPosition y. */
        public y: number;

        /** PlayerPosition z. */
        public z: number;

        /** PlayerPosition rotationX. */
        public rotationX: number;

        /** PlayerPosition rotationY. */
        public rotationY: number;

        /** PlayerPosition rotationZ. */
        public rotationZ: number;

        /** PlayerPosition rotationW. */
        public rotationW: number;

        /**
         * Creates a new PlayerPosition instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerPosition instance
         */
        public static create(properties?: game.IPlayerPosition): game.PlayerPosition;

        /**
         * Encodes the specified PlayerPosition message. Does not implicitly {@link game.PlayerPosition.verify|verify} messages.
         * @param message PlayerPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerPosition message, length delimited. Does not implicitly {@link game.PlayerPosition.verify|verify} messages.
         * @param message PlayerPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerPosition message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerPosition;

        /**
         * Decodes a PlayerPosition message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerPosition;

        /**
         * Verifies a PlayerPosition message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerPosition message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerPosition
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerPosition;

        /**
         * Creates a plain object from a PlayerPosition message. Also converts values to other types if specified.
         * @param message PlayerPosition
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerPosition, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerPosition to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerPosition
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerAnimation. */
    interface IPlayerAnimation {

        /** PlayerAnimation btcAddress */
        btcAddress?: (string|null);

        /** PlayerAnimation animationName */
        animationName?: (string|null);

        /** PlayerAnimation speed */
        speed?: (number|null);

        /** PlayerAnimation loop */
        loop?: (boolean|null);
    }

    /** Represents a PlayerAnimation. */
    class PlayerAnimation implements IPlayerAnimation {

        /**
         * Constructs a new PlayerAnimation.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerAnimation);

        /** PlayerAnimation btcAddress. */
        public btcAddress: string;

        /** PlayerAnimation animationName. */
        public animationName: string;

        /** PlayerAnimation speed. */
        public speed: number;

        /** PlayerAnimation loop. */
        public loop: boolean;

        /**
         * Creates a new PlayerAnimation instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerAnimation instance
         */
        public static create(properties?: game.IPlayerAnimation): game.PlayerAnimation;

        /**
         * Encodes the specified PlayerAnimation message. Does not implicitly {@link game.PlayerAnimation.verify|verify} messages.
         * @param message PlayerAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerAnimation message, length delimited. Does not implicitly {@link game.PlayerAnimation.verify|verify} messages.
         * @param message PlayerAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerAnimation message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerAnimation;

        /**
         * Decodes a PlayerAnimation message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerAnimation;

        /**
         * Verifies a PlayerAnimation message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerAnimation message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerAnimation
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerAnimation;

        /**
         * Creates a plain object from a PlayerAnimation message. Also converts values to other types if specified.
         * @param message PlayerAnimation
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerAnimation, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerAnimation to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerAnimation
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerUpdate. */
    interface IPlayerUpdate {

        /** PlayerUpdate btcAddress */
        btcAddress?: (string|null);

        /** PlayerUpdate key */
        key?: (string|null);

        /** PlayerUpdate value */
        value?: (string|null);
    }

    /** Represents a PlayerUpdate. */
    class PlayerUpdate implements IPlayerUpdate {

        /**
         * Constructs a new PlayerUpdate.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerUpdate);

        /** PlayerUpdate btcAddress. */
        public btcAddress: string;

        /** PlayerUpdate key. */
        public key: string;

        /** PlayerUpdate value. */
        public value: string;

        /**
         * Creates a new PlayerUpdate instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerUpdate instance
         */
        public static create(properties?: game.IPlayerUpdate): game.PlayerUpdate;

        /**
         * Encodes the specified PlayerUpdate message. Does not implicitly {@link game.PlayerUpdate.verify|verify} messages.
         * @param message PlayerUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerUpdate message, length delimited. Does not implicitly {@link game.PlayerUpdate.verify|verify} messages.
         * @param message PlayerUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerUpdate message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerUpdate;

        /**
         * Decodes a PlayerUpdate message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerUpdate;

        /**
         * Verifies a PlayerUpdate message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerUpdate message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerUpdate
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerUpdate;

        /**
         * Creates a plain object from a PlayerUpdate message. Also converts values to other types if specified.
         * @param message PlayerUpdate
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerUpdate, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerUpdate to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerUpdate
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerEnter. */
    interface IPlayerEnter {

        /** PlayerEnter btcAddress */
        btcAddress?: (string|null);

        /** PlayerEnter sessionId */
        sessionId?: (string|null);

        /** PlayerEnter avatarData */
        avatarData?: (game.IAvatarData|null);

        /** PlayerEnter position */
        position?: (game.IPlayerPosition|null);
    }

    /** Represents a PlayerEnter. */
    class PlayerEnter implements IPlayerEnter {

        /**
         * Constructs a new PlayerEnter.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerEnter);

        /** PlayerEnter btcAddress. */
        public btcAddress: string;

        /** PlayerEnter sessionId. */
        public sessionId: string;

        /** PlayerEnter avatarData. */
        public avatarData?: (game.IAvatarData|null);

        /** PlayerEnter position. */
        public position?: (game.IPlayerPosition|null);

        /**
         * Creates a new PlayerEnter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerEnter instance
         */
        public static create(properties?: game.IPlayerEnter): game.PlayerEnter;

        /**
         * Encodes the specified PlayerEnter message. Does not implicitly {@link game.PlayerEnter.verify|verify} messages.
         * @param message PlayerEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerEnter message, length delimited. Does not implicitly {@link game.PlayerEnter.verify|verify} messages.
         * @param message PlayerEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerEnter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerEnter;

        /**
         * Decodes a PlayerEnter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerEnter;

        /**
         * Verifies a PlayerEnter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerEnter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerEnter
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerEnter;

        /**
         * Creates a plain object from a PlayerEnter message. Also converts values to other types if specified.
         * @param message PlayerEnter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerEnter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerEnter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerEnter
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerLeave. */
    interface IPlayerLeave {

        /** PlayerLeave btcAddress */
        btcAddress?: (string|null);
    }

    /** Represents a PlayerLeave. */
    class PlayerLeave implements IPlayerLeave {

        /**
         * Constructs a new PlayerLeave.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerLeave);

        /** PlayerLeave btcAddress. */
        public btcAddress: string;

        /**
         * Creates a new PlayerLeave instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerLeave instance
         */
        public static create(properties?: game.IPlayerLeave): game.PlayerLeave;

        /**
         * Encodes the specified PlayerLeave message. Does not implicitly {@link game.PlayerLeave.verify|verify} messages.
         * @param message PlayerLeave message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerLeave, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerLeave message, length delimited. Does not implicitly {@link game.PlayerLeave.verify|verify} messages.
         * @param message PlayerLeave message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerLeave, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerLeave message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerLeave;

        /**
         * Decodes a PlayerLeave message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerLeave;

        /**
         * Verifies a PlayerLeave message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerLeave message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerLeave
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerLeave;

        /**
         * Creates a plain object from a PlayerLeave message. Also converts values to other types if specified.
         * @param message PlayerLeave
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerLeave, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerLeave to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerLeave
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an AvatarData. */
    interface IAvatarData {

        /** AvatarData shirtId */
        shirtId?: (string|null);

        /** AvatarData shirtTextureId */
        shirtTextureId?: (string|null);

        /** AvatarData shirtColor */
        shirtColor?: (string|null);

        /** AvatarData pantsId */
        pantsId?: (string|null);

        /** AvatarData shoesId */
        shoesId?: (string|null);

        /** AvatarData hatId */
        hatId?: (string|null);
    }

    /** Represents an AvatarData. */
    class AvatarData implements IAvatarData {

        /**
         * Constructs a new AvatarData.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IAvatarData);

        /** AvatarData shirtId. */
        public shirtId: string;

        /** AvatarData shirtTextureId. */
        public shirtTextureId: string;

        /** AvatarData shirtColor. */
        public shirtColor: string;

        /** AvatarData pantsId. */
        public pantsId: string;

        /** AvatarData shoesId. */
        public shoesId: string;

        /** AvatarData hatId. */
        public hatId: string;

        /**
         * Creates a new AvatarData instance using the specified properties.
         * @param [properties] Properties to set
         * @returns AvatarData instance
         */
        public static create(properties?: game.IAvatarData): game.AvatarData;

        /**
         * Encodes the specified AvatarData message. Does not implicitly {@link game.AvatarData.verify|verify} messages.
         * @param message AvatarData message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IAvatarData, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified AvatarData message, length delimited. Does not implicitly {@link game.AvatarData.verify|verify} messages.
         * @param message AvatarData message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IAvatarData, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an AvatarData message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns AvatarData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.AvatarData;

        /**
         * Decodes an AvatarData message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns AvatarData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.AvatarData;

        /**
         * Verifies an AvatarData message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an AvatarData message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns AvatarData
         */
        public static fromObject(object: { [k: string]: any }): game.AvatarData;

        /**
         * Creates a plain object from an AvatarData message. Also converts values to other types if specified.
         * @param message AvatarData
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.AvatarData, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this AvatarData to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for AvatarData
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PetPosition. */
    interface IPetPosition {

        /** PetPosition ownerBtcAddress */
        ownerBtcAddress?: (string|null);

        /** PetPosition petId */
        petId?: (string|null);

        /** PetPosition x */
        x?: (number|null);

        /** PetPosition y */
        y?: (number|null);

        /** PetPosition z */
        z?: (number|null);
    }

    /** Represents a PetPosition. */
    class PetPosition implements IPetPosition {

        /**
         * Constructs a new PetPosition.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPetPosition);

        /** PetPosition ownerBtcAddress. */
        public ownerBtcAddress: string;

        /** PetPosition petId. */
        public petId: string;

        /** PetPosition x. */
        public x: number;

        /** PetPosition y. */
        public y: number;

        /** PetPosition z. */
        public z: number;

        /**
         * Creates a new PetPosition instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PetPosition instance
         */
        public static create(properties?: game.IPetPosition): game.PetPosition;

        /**
         * Encodes the specified PetPosition message. Does not implicitly {@link game.PetPosition.verify|verify} messages.
         * @param message PetPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPetPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PetPosition message, length delimited. Does not implicitly {@link game.PetPosition.verify|verify} messages.
         * @param message PetPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPetPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PetPosition message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PetPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PetPosition;

        /**
         * Decodes a PetPosition message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PetPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PetPosition;

        /**
         * Verifies a PetPosition message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PetPosition message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PetPosition
         */
        public static fromObject(object: { [k: string]: any }): game.PetPosition;

        /**
         * Creates a plain object from a PetPosition message. Also converts values to other types if specified.
         * @param message PetPosition
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PetPosition, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PetPosition to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PetPosition
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PetAnimation. */
    interface IPetAnimation {

        /** PetAnimation ownerBtcAddress */
        ownerBtcAddress?: (string|null);

        /** PetAnimation petId */
        petId?: (string|null);

        /** PetAnimation animationName */
        animationName?: (string|null);
    }

    /** Represents a PetAnimation. */
    class PetAnimation implements IPetAnimation {

        /**
         * Constructs a new PetAnimation.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPetAnimation);

        /** PetAnimation ownerBtcAddress. */
        public ownerBtcAddress: string;

        /** PetAnimation petId. */
        public petId: string;

        /** PetAnimation animationName. */
        public animationName: string;

        /**
         * Creates a new PetAnimation instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PetAnimation instance
         */
        public static create(properties?: game.IPetAnimation): game.PetAnimation;

        /**
         * Encodes the specified PetAnimation message. Does not implicitly {@link game.PetAnimation.verify|verify} messages.
         * @param message PetAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPetAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PetAnimation message, length delimited. Does not implicitly {@link game.PetAnimation.verify|verify} messages.
         * @param message PetAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPetAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PetAnimation message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PetAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PetAnimation;

        /**
         * Decodes a PetAnimation message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PetAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PetAnimation;

        /**
         * Verifies a PetAnimation message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PetAnimation message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PetAnimation
         */
        public static fromObject(object: { [k: string]: any }): game.PetAnimation;

        /**
         * Creates a plain object from a PetAnimation message. Also converts values to other types if specified.
         * @param message PetAnimation
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PetAnimation, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PetAnimation to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PetAnimation
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ChatEnter. */
    interface IChatEnter {

        /** ChatEnter chatId */
        chatId?: (number|null);

        /** ChatEnter btcAddress */
        btcAddress?: (string|null);
    }

    /** Represents a ChatEnter. */
    class ChatEnter implements IChatEnter {

        /**
         * Constructs a new ChatEnter.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IChatEnter);

        /** ChatEnter chatId. */
        public chatId: number;

        /** ChatEnter btcAddress. */
        public btcAddress: string;

        /**
         * Creates a new ChatEnter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ChatEnter instance
         */
        public static create(properties?: game.IChatEnter): game.ChatEnter;

        /**
         * Encodes the specified ChatEnter message. Does not implicitly {@link game.ChatEnter.verify|verify} messages.
         * @param message ChatEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IChatEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ChatEnter message, length delimited. Does not implicitly {@link game.ChatEnter.verify|verify} messages.
         * @param message ChatEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IChatEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ChatEnter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ChatEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ChatEnter;

        /**
         * Decodes a ChatEnter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ChatEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ChatEnter;

        /**
         * Verifies a ChatEnter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ChatEnter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ChatEnter
         */
        public static fromObject(object: { [k: string]: any }): game.ChatEnter;

        /**
         * Creates a plain object from a ChatEnter message. Also converts values to other types if specified.
         * @param message ChatEnter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ChatEnter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ChatEnter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ChatEnter
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ChatLeave. */
    interface IChatLeave {

        /** ChatLeave chatId */
        chatId?: (number|null);

        /** ChatLeave btcAddress */
        btcAddress?: (string|null);
    }

    /** Represents a ChatLeave. */
    class ChatLeave implements IChatLeave {

        /**
         * Constructs a new ChatLeave.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IChatLeave);

        /** ChatLeave chatId. */
        public chatId: number;

        /** ChatLeave btcAddress. */
        public btcAddress: string;

        /**
         * Creates a new ChatLeave instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ChatLeave instance
         */
        public static create(properties?: game.IChatLeave): game.ChatLeave;

        /**
         * Encodes the specified ChatLeave message. Does not implicitly {@link game.ChatLeave.verify|verify} messages.
         * @param message ChatLeave message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IChatLeave, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ChatLeave message, length delimited. Does not implicitly {@link game.ChatLeave.verify|verify} messages.
         * @param message ChatLeave message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IChatLeave, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ChatLeave message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ChatLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ChatLeave;

        /**
         * Decodes a ChatLeave message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ChatLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ChatLeave;

        /**
         * Verifies a ChatLeave message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ChatLeave message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ChatLeave
         */
        public static fromObject(object: { [k: string]: any }): game.ChatLeave;

        /**
         * Creates a plain object from a ChatLeave message. Also converts values to other types if specified.
         * @param message ChatLeave
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ChatLeave, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ChatLeave to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ChatLeave
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ChatMessage. */
    interface IChatMessage {

        /** ChatMessage uuid */
        uuid?: (string|null);

        /** ChatMessage playerId */
        playerId?: (string|null);

        /** ChatMessage content */
        content?: (string|null);

        /** ChatMessage replyTo */
        replyTo?: (string|null);

        /** ChatMessage timestamp */
        timestamp?: (number|Long|null);

        /** ChatMessage tabType */
        tabType?: (number|null);
    }

    /** Represents a ChatMessage. */
    class ChatMessage implements IChatMessage {

        /**
         * Constructs a new ChatMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IChatMessage);

        /** ChatMessage uuid. */
        public uuid: string;

        /** ChatMessage playerId. */
        public playerId: string;

        /** ChatMessage content. */
        public content: string;

        /** ChatMessage replyTo. */
        public replyTo: string;

        /** ChatMessage timestamp. */
        public timestamp: (number|Long);

        /** ChatMessage tabType. */
        public tabType: number;

        /**
         * Creates a new ChatMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ChatMessage instance
         */
        public static create(properties?: game.IChatMessage): game.ChatMessage;

        /**
         * Encodes the specified ChatMessage message. Does not implicitly {@link game.ChatMessage.verify|verify} messages.
         * @param message ChatMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IChatMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ChatMessage message, length delimited. Does not implicitly {@link game.ChatMessage.verify|verify} messages.
         * @param message ChatMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IChatMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ChatMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ChatMessage;

        /**
         * Decodes a ChatMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ChatMessage;

        /**
         * Verifies a ChatMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ChatMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ChatMessage
         */
        public static fromObject(object: { [k: string]: any }): game.ChatMessage;

        /**
         * Creates a plain object from a ChatMessage message. Also converts values to other types if specified.
         * @param message ChatMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ChatMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ChatMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ChatMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RedPacketUpdate. */
    interface IRedPacketUpdate {

        /** RedPacketUpdate packetId */
        packetId?: (string|null);

        /** RedPacketUpdate creatorAddress */
        creatorAddress?: (string|null);

        /** RedPacketUpdate amount */
        amount?: (number|null);

        /** RedPacketUpdate count */
        count?: (number|null);

        /** RedPacketUpdate remaining */
        remaining?: (number|null);

        /** RedPacketUpdate status */
        status?: (string|null);

        /** RedPacketUpdate createdAt */
        createdAt?: (number|Long|null);

        /** RedPacketUpdate expiresAt */
        expiresAt?: (number|Long|null);
    }

    /** Represents a RedPacketUpdate. */
    class RedPacketUpdate implements IRedPacketUpdate {

        /**
         * Constructs a new RedPacketUpdate.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IRedPacketUpdate);

        /** RedPacketUpdate packetId. */
        public packetId: string;

        /** RedPacketUpdate creatorAddress. */
        public creatorAddress: string;

        /** RedPacketUpdate amount. */
        public amount: number;

        /** RedPacketUpdate count. */
        public count: number;

        /** RedPacketUpdate remaining. */
        public remaining: number;

        /** RedPacketUpdate status. */
        public status: string;

        /** RedPacketUpdate createdAt. */
        public createdAt: (number|Long);

        /** RedPacketUpdate expiresAt. */
        public expiresAt: (number|Long);

        /**
         * Creates a new RedPacketUpdate instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RedPacketUpdate instance
         */
        public static create(properties?: game.IRedPacketUpdate): game.RedPacketUpdate;

        /**
         * Encodes the specified RedPacketUpdate message. Does not implicitly {@link game.RedPacketUpdate.verify|verify} messages.
         * @param message RedPacketUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IRedPacketUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RedPacketUpdate message, length delimited. Does not implicitly {@link game.RedPacketUpdate.verify|verify} messages.
         * @param message RedPacketUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IRedPacketUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RedPacketUpdate message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RedPacketUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.RedPacketUpdate;

        /**
         * Decodes a RedPacketUpdate message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RedPacketUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.RedPacketUpdate;

        /**
         * Verifies a RedPacketUpdate message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RedPacketUpdate message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RedPacketUpdate
         */
        public static fromObject(object: { [k: string]: any }): game.RedPacketUpdate;

        /**
         * Creates a plain object from a RedPacketUpdate message. Also converts values to other types if specified.
         * @param message RedPacketUpdate
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.RedPacketUpdate, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RedPacketUpdate to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RedPacketUpdate
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RedPacketReward. */
    interface IRedPacketReward {

        /** RedPacketReward packetId */
        packetId?: (string|null);

        /** RedPacketReward receiverAddress */
        receiverAddress?: (string|null);

        /** RedPacketReward amount */
        amount?: (number|null);

        /** RedPacketReward receivedAt */
        receivedAt?: (number|Long|null);
    }

    /** Represents a RedPacketReward. */
    class RedPacketReward implements IRedPacketReward {

        /**
         * Constructs a new RedPacketReward.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IRedPacketReward);

        /** RedPacketReward packetId. */
        public packetId: string;

        /** RedPacketReward receiverAddress. */
        public receiverAddress: string;

        /** RedPacketReward amount. */
        public amount: number;

        /** RedPacketReward receivedAt. */
        public receivedAt: (number|Long);

        /**
         * Creates a new RedPacketReward instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RedPacketReward instance
         */
        public static create(properties?: game.IRedPacketReward): game.RedPacketReward;

        /**
         * Encodes the specified RedPacketReward message. Does not implicitly {@link game.RedPacketReward.verify|verify} messages.
         * @param message RedPacketReward message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IRedPacketReward, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RedPacketReward message, length delimited. Does not implicitly {@link game.RedPacketReward.verify|verify} messages.
         * @param message RedPacketReward message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IRedPacketReward, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RedPacketReward message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RedPacketReward
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.RedPacketReward;

        /**
         * Decodes a RedPacketReward message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RedPacketReward
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.RedPacketReward;

        /**
         * Verifies a RedPacketReward message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RedPacketReward message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RedPacketReward
         */
        public static fromObject(object: { [k: string]: any }): game.RedPacketReward;

        /**
         * Creates a plain object from a RedPacketReward message. Also converts values to other types if specified.
         * @param message RedPacketReward
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.RedPacketReward, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RedPacketReward to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RedPacketReward
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an ErrorMessage. */
    interface IErrorMessage {

        /** ErrorMessage code */
        code?: (number|null);

        /** ErrorMessage message */
        message?: (string|null);

        /** ErrorMessage details */
        details?: (string|null);
    }

    /** Represents an ErrorMessage. */
    class ErrorMessage implements IErrorMessage {

        /**
         * Constructs a new ErrorMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IErrorMessage);

        /** ErrorMessage code. */
        public code: number;

        /** ErrorMessage message. */
        public message: string;

        /** ErrorMessage details. */
        public details: string;

        /**
         * Creates a new ErrorMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ErrorMessage instance
         */
        public static create(properties?: game.IErrorMessage): game.ErrorMessage;

        /**
         * Encodes the specified ErrorMessage message. Does not implicitly {@link game.ErrorMessage.verify|verify} messages.
         * @param message ErrorMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IErrorMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ErrorMessage message, length delimited. Does not implicitly {@link game.ErrorMessage.verify|verify} messages.
         * @param message ErrorMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IErrorMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an ErrorMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ErrorMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ErrorMessage;

        /**
         * Decodes an ErrorMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ErrorMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ErrorMessage;

        /**
         * Verifies an ErrorMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an ErrorMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ErrorMessage
         */
        public static fromObject(object: { [k: string]: any }): game.ErrorMessage;

        /**
         * Creates a plain object from an ErrorMessage message. Also converts values to other types if specified.
         * @param message ErrorMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ErrorMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ErrorMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ErrorMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a HeartbeatMessage. */
    interface IHeartbeatMessage {

        /** HeartbeatMessage clientTimestamp */
        clientTimestamp?: (number|Long|null);

        /** HeartbeatMessage serverTimestamp */
        serverTimestamp?: (number|Long|null);
    }

    /** Represents a HeartbeatMessage. */
    class HeartbeatMessage implements IHeartbeatMessage {

        /**
         * Constructs a new HeartbeatMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IHeartbeatMessage);

        /** HeartbeatMessage clientTimestamp. */
        public clientTimestamp: (number|Long);

        /** HeartbeatMessage serverTimestamp. */
        public serverTimestamp: (number|Long);

        /**
         * Creates a new HeartbeatMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns HeartbeatMessage instance
         */
        public static create(properties?: game.IHeartbeatMessage): game.HeartbeatMessage;

        /**
         * Encodes the specified HeartbeatMessage message. Does not implicitly {@link game.HeartbeatMessage.verify|verify} messages.
         * @param message HeartbeatMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IHeartbeatMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified HeartbeatMessage message, length delimited. Does not implicitly {@link game.HeartbeatMessage.verify|verify} messages.
         * @param message HeartbeatMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IHeartbeatMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a HeartbeatMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns HeartbeatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.HeartbeatMessage;

        /**
         * Decodes a HeartbeatMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns HeartbeatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.HeartbeatMessage;

        /**
         * Verifies a HeartbeatMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a HeartbeatMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns HeartbeatMessage
         */
        public static fromObject(object: { [k: string]: any }): game.HeartbeatMessage;

        /**
         * Creates a plain object from a HeartbeatMessage message. Also converts values to other types if specified.
         * @param message HeartbeatMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.HeartbeatMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this HeartbeatMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for HeartbeatMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a LoginRequest. */
    interface ILoginRequest {

        /** LoginRequest btcAddress */
        btcAddress?: (string|null);

        /** LoginRequest sessionId */
        sessionId?: (string|null);
    }

    /** Represents a LoginRequest. */
    class LoginRequest implements ILoginRequest {

        /**
         * Constructs a new LoginRequest.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.ILoginRequest);

        /** LoginRequest btcAddress. */
        public btcAddress: string;

        /** LoginRequest sessionId. */
        public sessionId: string;

        /**
         * Creates a new LoginRequest instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LoginRequest instance
         */
        public static create(properties?: game.ILoginRequest): game.LoginRequest;

        /**
         * Encodes the specified LoginRequest message. Does not implicitly {@link game.LoginRequest.verify|verify} messages.
         * @param message LoginRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.ILoginRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LoginRequest message, length delimited. Does not implicitly {@link game.LoginRequest.verify|verify} messages.
         * @param message LoginRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.ILoginRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LoginRequest message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LoginRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.LoginRequest;

        /**
         * Decodes a LoginRequest message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LoginRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.LoginRequest;

        /**
         * Verifies a LoginRequest message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LoginRequest message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LoginRequest
         */
        public static fromObject(object: { [k: string]: any }): game.LoginRequest;

        /**
         * Creates a plain object from a LoginRequest message. Also converts values to other types if specified.
         * @param message LoginRequest
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.LoginRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LoginRequest to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for LoginRequest
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a LoginResponse. */
    interface ILoginResponse {

        /** LoginResponse success */
        success?: (boolean|null);

        /** LoginResponse message */
        message?: (string|null);

        /** LoginResponse serverTime */
        serverTime?: (number|Long|null);
    }

    /** Represents a LoginResponse. */
    class LoginResponse implements ILoginResponse {

        /**
         * Constructs a new LoginResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.ILoginResponse);

        /** LoginResponse success. */
        public success: boolean;

        /** LoginResponse message. */
        public message: string;

        /** LoginResponse serverTime. */
        public serverTime: (number|Long);

        /**
         * Creates a new LoginResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LoginResponse instance
         */
        public static create(properties?: game.ILoginResponse): game.LoginResponse;

        /**
         * Encodes the specified LoginResponse message. Does not implicitly {@link game.LoginResponse.verify|verify} messages.
         * @param message LoginResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.ILoginResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LoginResponse message, length delimited. Does not implicitly {@link game.LoginResponse.verify|verify} messages.
         * @param message LoginResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.ILoginResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LoginResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LoginResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.LoginResponse;

        /**
         * Decodes a LoginResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LoginResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.LoginResponse;

        /**
         * Verifies a LoginResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LoginResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LoginResponse
         */
        public static fromObject(object: { [k: string]: any }): game.LoginResponse;

        /**
         * Creates a plain object from a LoginResponse message. Also converts values to other types if specified.
         * @param message LoginResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.LoginResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LoginResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for LoginResponse
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RoomListRequest. */
    interface IRoomListRequest {

        /** RoomListRequest page */
        page?: (number|null);

        /** RoomListRequest limit */
        limit?: (number|null);

        /** RoomListRequest filter */
        filter?: (string|null);
    }

    /** Represents a RoomListRequest. */
    class RoomListRequest implements IRoomListRequest {

        /**
         * Constructs a new RoomListRequest.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IRoomListRequest);

        /** RoomListRequest page. */
        public page: number;

        /** RoomListRequest limit. */
        public limit: number;

        /** RoomListRequest filter. */
        public filter: string;

        /**
         * Creates a new RoomListRequest instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RoomListRequest instance
         */
        public static create(properties?: game.IRoomListRequest): game.RoomListRequest;

        /**
         * Encodes the specified RoomListRequest message. Does not implicitly {@link game.RoomListRequest.verify|verify} messages.
         * @param message RoomListRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IRoomListRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RoomListRequest message, length delimited. Does not implicitly {@link game.RoomListRequest.verify|verify} messages.
         * @param message RoomListRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IRoomListRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RoomListRequest message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RoomListRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.RoomListRequest;

        /**
         * Decodes a RoomListRequest message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RoomListRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.RoomListRequest;

        /**
         * Verifies a RoomListRequest message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RoomListRequest message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RoomListRequest
         */
        public static fromObject(object: { [k: string]: any }): game.RoomListRequest;

        /**
         * Creates a plain object from a RoomListRequest message. Also converts values to other types if specified.
         * @param message RoomListRequest
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.RoomListRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RoomListRequest to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RoomListRequest
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RoomInfo. */
    interface IRoomInfo {

        /** RoomInfo roomId */
        roomId?: (string|null);

        /** RoomInfo name */
        name?: (string|null);

        /** RoomInfo currentPlayers */
        currentPlayers?: (number|null);

        /** RoomInfo maxPlayers */
        maxPlayers?: (number|null);

        /** RoomInfo status */
        status?: (string|null);

        /** RoomInfo mapId */
        mapId?: (string|null);
    }

    /** Represents a RoomInfo. */
    class RoomInfo implements IRoomInfo {

        /**
         * Constructs a new RoomInfo.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IRoomInfo);

        /** RoomInfo roomId. */
        public roomId: string;

        /** RoomInfo name. */
        public name: string;

        /** RoomInfo currentPlayers. */
        public currentPlayers: number;

        /** RoomInfo maxPlayers. */
        public maxPlayers: number;

        /** RoomInfo status. */
        public status: string;

        /** RoomInfo mapId. */
        public mapId: string;

        /**
         * Creates a new RoomInfo instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RoomInfo instance
         */
        public static create(properties?: game.IRoomInfo): game.RoomInfo;

        /**
         * Encodes the specified RoomInfo message. Does not implicitly {@link game.RoomInfo.verify|verify} messages.
         * @param message RoomInfo message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IRoomInfo, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RoomInfo message, length delimited. Does not implicitly {@link game.RoomInfo.verify|verify} messages.
         * @param message RoomInfo message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IRoomInfo, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RoomInfo message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RoomInfo
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.RoomInfo;

        /**
         * Decodes a RoomInfo message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RoomInfo
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.RoomInfo;

        /**
         * Verifies a RoomInfo message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RoomInfo message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RoomInfo
         */
        public static fromObject(object: { [k: string]: any }): game.RoomInfo;

        /**
         * Creates a plain object from a RoomInfo message. Also converts values to other types if specified.
         * @param message RoomInfo
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.RoomInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RoomInfo to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RoomInfo
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RoomListResponse. */
    interface IRoomListResponse {

        /** RoomListResponse rooms */
        rooms?: (game.IRoomInfo[]|null);

        /** RoomListResponse totalCount */
        totalCount?: (number|null);

        /** RoomListResponse page */
        page?: (number|null);
    }

    /** Represents a RoomListResponse. */
    class RoomListResponse implements IRoomListResponse {

        /**
         * Constructs a new RoomListResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IRoomListResponse);

        /** RoomListResponse rooms. */
        public rooms: game.IRoomInfo[];

        /** RoomListResponse totalCount. */
        public totalCount: number;

        /** RoomListResponse page. */
        public page: number;

        /**
         * Creates a new RoomListResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RoomListResponse instance
         */
        public static create(properties?: game.IRoomListResponse): game.RoomListResponse;

        /**
         * Encodes the specified RoomListResponse message. Does not implicitly {@link game.RoomListResponse.verify|verify} messages.
         * @param message RoomListResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IRoomListResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RoomListResponse message, length delimited. Does not implicitly {@link game.RoomListResponse.verify|verify} messages.
         * @param message RoomListResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IRoomListResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RoomListResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RoomListResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.RoomListResponse;

        /**
         * Decodes a RoomListResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RoomListResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.RoomListResponse;

        /**
         * Verifies a RoomListResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RoomListResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RoomListResponse
         */
        public static fromObject(object: { [k: string]: any }): game.RoomListResponse;

        /**
         * Creates a plain object from a RoomListResponse message. Also converts values to other types if specified.
         * @param message RoomListResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.RoomListResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RoomListResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RoomListResponse
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }
}
