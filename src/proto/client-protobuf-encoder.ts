import { game } from './generated/game_messages';
import { C2S_PacketTypes, ClientRequestTypes } from 'common/types';

/**
 * 客户端Protobuf编码工具
 * 用于测试和示例，帮助客户端编码protobuf数据
 */
export class ClientProtobufEncoder {
  private static instance: ClientProtobufEncoder;

  public static getInstance(): ClientProtobufEncoder {
    if (!ClientProtobufEncoder.instance) {
      ClientProtobufEncoder.instance = new ClientProtobufEncoder();
    }
    return ClientProtobufEncoder.instance;
  }

  /**
   * 编码玩家动作消息
   * @param pid 动作类型
   * @param data 动作数据
   * @param btcAddress 玩家地址
   * @returns protobuf二进制数据
   */
  public encodePlayerAction(pid: C2S_PacketTypes, data: any, btcAddress: string): Buffer {
    try {
      let actionData: Uint8Array = new Uint8Array(0);

      // 根据动作类型编码内部数据
      switch (pid) {
        case C2S_PacketTypes.PlayerEnter:
          if (data.avatarData) {
            const enterData = game.ClientPlayerEnter.create({
              avatarData: this.convertAvatarDataToProto(data.avatarData)
            });
            actionData = game.ClientPlayerEnter.encode(enterData).finish();
          }
          break;

        case C2S_PacketTypes.PlayerPosition:
          if (data.position || data.x !== undefined) {
            const positionData = game.ClientPlayerPosition.create({
              x: data.position?.x || data.x || 0,
              y: data.position?.y || data.y || 0,
              z: data.position?.z || data.z || 0,
              rotationX: data.quaternion?.x || data.rotation_x || 0,
              rotationY: data.quaternion?.y || data.rotation_y || 0,
              rotationZ: data.quaternion?.z || data.rotation_z || 0,
              rotationW: data.quaternion?.w || data.rotation_w || 1,
            });
            actionData = game.ClientPlayerPosition.encode(positionData).finish();
          }
          break;

        case C2S_PacketTypes.PlayerAnimation:
          if (data.curAnimation) {
            const animationData = game.ClientPlayerAnimation.create({
              curAnimation: data.curAnimation,
              speed: data.speed || 1.0,
              loop: data.loop || false
            });
            actionData = game.ClientPlayerAnimation.encode(animationData).finish();
          }
          break;

        case C2S_PacketTypes.PlayerUpdate:
          const updateData = game.ClientPlayerUpdate.create({
            itemId: data.itemId || '',
            pizzaCount: data.pizzaCount || 0,
            petId: data.petId || ''
          });
          actionData = game.ClientPlayerUpdate.encode(updateData).finish();
          break;

        // 其他动作类型可以不编码内部数据，或使用空数据
        default:
          actionData = new Uint8Array(0);
          break;
      }

      // 创建动作消息包装器
      const actionMessage = game.PlayerActionMessage.create({
        pid: pid,
        actionData: actionData,
        btcAddress: btcAddress,
        timestamp: Date.now()
      });

      return Buffer.from(game.PlayerActionMessage.encode(actionMessage).finish());
    } catch (error) {
      console.error('Error encoding player action:', error);
      throw error;
    }
  }

  /**
   * 编码玩家请求消息
   * @param pid 请求类型
   * @param data 请求数据
   * @param btcAddress 玩家地址
   * @returns protobuf二进制数据
   */
  public encodePlayerRequest(pid: ClientRequestTypes, data: any, btcAddress: string): Buffer {
    try {
      // 对于请求数据，通常使用JSON字符串编码
      const jsonString = JSON.stringify(data);
      const requestData = Buffer.from(jsonString, 'utf8');

      // 创建请求消息包装器
      const requestMessage = game.PlayerRequestMessage.create({
        pid: pid,
        requestData: requestData,
        btcAddress: btcAddress,
        timestamp: Date.now()
      });

      return Buffer.from(game.PlayerRequestMessage.encode(requestMessage).finish());
    } catch (error) {
      console.error('Error encoding player request:', error);
      throw error;
    }
  }

  /**
   * 编码玩家聊天消息
   * @param chatId 聊天房间ID
   * @param pid 聊天消息类型
   * @param data 聊天数据
   * @param btcAddress 玩家地址
   * @returns protobuf二进制数据
   */
  public encodePlayerChat(chatId: number, pid: C2S_PacketTypes, data: any, btcAddress: string): Buffer {
    try {
      let chatData: Uint8Array = new Uint8Array(0);

      // 如果是聊天消息，编码聊天内容
      if (pid === C2S_PacketTypes.ChatMessage && data) {
        const chatMessage = game.ChatMessage.create({
          uuid: data.uuid || '',
          playerId: data.playerId || btcAddress,
          content: data.content || '',
          replyTo: data.replyTo || '',
          timestamp: data.timestamp || Date.now(),
          tabType: data.tabType || chatId
        });
        chatData = game.ChatMessage.encode(chatMessage).finish();
      }

      // 创建聊天消息包装器
      const chatMessage = game.PlayerChatMessage.create({
        chatId: chatId,
        pid: pid,
        chatData: chatData,
        btcAddress: btcAddress,
        timestamp: Date.now()
      });

      return Buffer.from(game.PlayerChatMessage.encode(chatMessage).finish());
    } catch (error) {
      console.error('Error encoding player chat:', error);
      throw error;
    }
  }

  /**
   * 转换头像数据到protobuf格式
   */
  private convertAvatarDataToProto(avatarData: any): any {
    if (!avatarData) return null;

    return {
      shirtId: avatarData.shirtId || '',
      shirtTextureId: avatarData.shirtTextureId || '',
      shirtColor: avatarData.shirtColor || '',
      pantsId: avatarData.pantsId || '',
      shoesId: avatarData.shoesId || '',
      hatId: avatarData.hatId || '',
    };
  }
}

export default ClientProtobufEncoder;
