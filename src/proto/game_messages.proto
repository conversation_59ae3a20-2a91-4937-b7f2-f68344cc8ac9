syntax = "proto3";

package game;

// 基础消息类型枚举
enum PacketType {
  // 玩家相关消息 (1-49)
  PLAYER_ENTER = 1;
  PLAYER_LEAVE = 2;
  PLAYER_POSITION = 10;
  PLAYER_ANIMATION = 11;
  PLAYER_UPDATE = 12;
  
  // 宠物相关消息 (50-99)
  PET_POSITION = 50;
  PET_ANIMATION = 51;
  
  // 聊天相关消息 (100-149)
  CHAT_ENTER = 100;
  CHAT_LEAVE = 101;
  CHAT_MESSAGE = 102;
  
  // 红包相关消息 (200-249)
  RED_PACKET_UPDATE = 201;
  RED_PACKET_REWARD = 202;
}

// 基础消息包装器
message GameMessage {
  PacketType packet_type = 1;
  bytes data = 2;
  int64 timestamp = 3;
}

// 玩家位置信息
message PlayerPosition {
  string btc_address = 1;
  float x = 2;
  float y = 3;
  float z = 4;
  float rotation_x = 5;
  float rotation_y = 6;
  float rotation_z = 7;
  float rotation_w = 8;
}

// 玩家动画信息
message PlayerAnimation {
  string btc_address = 1;
  string animation_name = 2;
  float speed = 3;
  bool loop = 4;
}

// 玩家更新信息
message PlayerUpdate {
  string btc_address = 1;
  string key = 2;
  string value = 3;
}

// 玩家进入信息
message PlayerEnter {
  string btc_address = 1;
  string session_id = 2;
  AvatarData avatar_data = 3;
  PlayerPosition position = 4;
}

// 玩家离开信息
message PlayerLeave {
  string btc_address = 1;
}

// 头像数据
message AvatarData {
  string shirt_id = 1;
  string shirt_texture_id = 2;
  string shirt_color = 3;
  string pants_id = 4;
  string shoes_id = 5;
  string hat_id = 6;
}

// 宠物位置信息
message PetPosition {
  string owner_btc_address = 1;
  string pet_id = 2;
  float x = 3;
  float y = 4;
  float z = 5;
}

// 宠物动画信息
message PetAnimation {
  string owner_btc_address = 1;
  string pet_id = 2;
  string animation_name = 3;
}

// 聊天进入信息
message ChatEnter {
  int32 chat_id = 1;
  string btc_address = 2;
}

// 聊天离开信息
message ChatLeave {
  int32 chat_id = 1;
  string btc_address = 2;
}

// 聊天消息
message ChatMessage {
  string uuid = 1;
  string player_id = 2;
  string content = 3;
  string reply_to = 4;
  int64 timestamp = 5;
  int32 tab_type = 6;
}

// 红包更新信息
message RedPacketUpdate {
  string packet_id = 1;
  string creator_address = 2;
  float amount = 3;
  int32 count = 4;
  int32 remaining = 5;
  string status = 6;
  int64 created_at = 7;
  int64 expires_at = 8;
}

// 红包奖励信息
message RedPacketReward {
  string packet_id = 1;
  string receiver_address = 2;
  float amount = 3;
  int64 received_at = 4;
}

// 错误消息
message ErrorMessage {
  int32 code = 1;
  string message = 2;
  string details = 3;
}

// 心跳消息
message HeartbeatMessage {
  int64 client_timestamp = 1;
  int64 server_timestamp = 2;
}

// 登录请求
message LoginRequest {
  string btc_address = 1;
  string session_id = 2;
}

// 登录响应
message LoginResponse {
  bool success = 1;
  string message = 2;
  int64 server_time = 3;
}

// 房间列表请求
message RoomListRequest {
  int32 page = 1;
  int32 limit = 2;
  string filter = 3;
}

// 房间信息
message RoomInfo {
  string room_id = 1;
  string name = 2;
  int32 current_players = 3;
  int32 max_players = 4;
  string status = 5;
  string map_id = 6;
}

// 房间列表响应
message RoomListResponse {
  repeated RoomInfo rooms = 1;
  int32 total_count = 2;
  int32 page = 3;
}
