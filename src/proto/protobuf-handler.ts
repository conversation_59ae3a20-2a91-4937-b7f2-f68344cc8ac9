import * as protobuf from 'protobufjs';
import { game } from './generated/game_messages';
import { S2C_PacketTypes, C2S_PacketTypes } from 'common/types';

/**
 * Protobuf消息处理器
 * 负责在JSON和Protobuf之间进行转换
 */
export class ProtobufHandler {
  private static instance: ProtobufHandler;
  
  // 消息类型映射：从枚举到protobuf消息类型
  private readonly s2cMessageMap = new Map<S2C_PacketTypes, any>([
    [S2C_PacketTypes.PlayerEnter, game.PlayerEnter],
    [S2C_PacketTypes.PlayerLeave, game.PlayerLeave],
    [S2C_PacketTypes.PlayerPosition, game.PlayerPosition],
    [S2C_PacketTypes.PlayerAnimation, game.PlayerAnimation],
    [S2C_PacketTypes.PlayerUpdate, game.PlayerUpdate],
    [S2C_PacketTypes.PetPosition, game.PetPosition],
    [S2C_PacketTypes.PetAnimation, game.PetAnimation],
    [S2C_PacketTypes.ChatEnter, game.ChatEnter],
    [S2C_PacketTypes.ChatLeave, game.ChatLeave],
    [S2C_PacketTypes.ChatMessage, game.ChatMessage],
    [S2C_PacketTypes.RedPacketUpdate, game.RedPacketUpdate],
  ]);

  private readonly c2sMessageMap = new Map<C2S_PacketTypes, any>([
    [C2S_PacketTypes.PlayerEnter, game.PlayerEnter],
    [C2S_PacketTypes.PlayerLeave, game.PlayerLeave],
    [C2S_PacketTypes.PlayerPosition, game.PlayerPosition],
    [C2S_PacketTypes.PlayerAnimation, game.PlayerAnimation],
    [C2S_PacketTypes.PlayerUpdate, game.PlayerUpdate],
    [C2S_PacketTypes.PetPosition, game.PetPosition],
    [C2S_PacketTypes.PetAnimation, game.PetAnimation],
    [C2S_PacketTypes.ChatEnter, game.ChatEnter],
    [C2S_PacketTypes.ChatLeave, game.ChatLeave],
    [C2S_PacketTypes.ChatMessage, game.ChatMessage],
  ]);

  // Protobuf PacketType到枚举的映射
  private readonly protoToS2CMap = new Map<game.PacketType, S2C_PacketTypes>([
    [game.PacketType.PLAYER_ENTER, S2C_PacketTypes.PlayerEnter],
    [game.PacketType.PLAYER_LEAVE, S2C_PacketTypes.PlayerLeave],
    [game.PacketType.PLAYER_POSITION, S2C_PacketTypes.PlayerPosition],
    [game.PacketType.PLAYER_ANIMATION, S2C_PacketTypes.PlayerAnimation],
    [game.PacketType.PLAYER_UPDATE, S2C_PacketTypes.PlayerUpdate],
    [game.PacketType.PET_POSITION, S2C_PacketTypes.PetPosition],
    [game.PacketType.PET_ANIMATION, S2C_PacketTypes.PetAnimation],
    [game.PacketType.CHAT_ENTER, S2C_PacketTypes.ChatEnter],
    [game.PacketType.CHAT_LEAVE, S2C_PacketTypes.ChatLeave],
    [game.PacketType.CHAT_MESSAGE, S2C_PacketTypes.ChatMessage],
    [game.PacketType.RED_PACKET_UPDATE, S2C_PacketTypes.RedPacketUpdate],
  ]);

  private readonly s2cToProtoMap = new Map<S2C_PacketTypes, game.PacketType>([
    [S2C_PacketTypes.PlayerEnter, game.PacketType.PLAYER_ENTER],
    [S2C_PacketTypes.PlayerLeave, game.PacketType.PLAYER_LEAVE],
    [S2C_PacketTypes.PlayerPosition, game.PacketType.PLAYER_POSITION],
    [S2C_PacketTypes.PlayerAnimation, game.PacketType.PLAYER_ANIMATION],
    [S2C_PacketTypes.PlayerUpdate, game.PacketType.PLAYER_UPDATE],
    [S2C_PacketTypes.PetPosition, game.PacketType.PET_POSITION],
    [S2C_PacketTypes.PetAnimation, game.PacketType.PET_ANIMATION],
    [S2C_PacketTypes.ChatEnter, game.PacketType.CHAT_ENTER],
    [S2C_PacketTypes.ChatLeave, game.PacketType.CHAT_LEAVE],
    [S2C_PacketTypes.ChatMessage, game.PacketType.CHAT_MESSAGE],
    [S2C_PacketTypes.RedPacketUpdate, game.PacketType.RED_PACKET_UPDATE],
  ]);

  public static getInstance(): ProtobufHandler {
    if (!ProtobufHandler.instance) {
      ProtobufHandler.instance = new ProtobufHandler();
    }
    return ProtobufHandler.instance;
  }

  /**
   * 将JSON数据编码为protobuf二进制数据
   * @param packetType 消息类型
   * @param data JSON数据
   * @returns protobuf二进制数据
   */
  public encodeMessage(packetType: S2C_PacketTypes, data: any): Buffer {
    try {
      const MessageClass = this.s2cMessageMap.get(packetType);
      if (!MessageClass) {
        throw new Error(`Unsupported packet type: ${packetType}`);
      }

      // 转换数据格式以匹配protobuf字段命名
      const protobufData = this.convertToProtobufFormat(data, packetType);
      
      // 创建消息实例
      const message = MessageClass.create(protobufData);
      
      // 编码为二进制
      const messageBuffer = MessageClass.encode(message).finish();
      
      // 创建包装消息
      const gameMessage = game.GameMessage.create({
        packetType: this.s2cToProtoMap.get(packetType),
        data: messageBuffer,
        timestamp: Date.now()
      });
      
      return Buffer.from(game.GameMessage.encode(gameMessage).finish());
    } catch (error) {
      console.error('Error encoding protobuf message:', error);
      throw error;
    }
  }

  /**
   * 将protobuf二进制数据解码为JSON
   * @param buffer protobuf二进制数据
   * @returns 解码后的JSON数据
   */
  public decodeMessage(buffer: Buffer): { packetType: S2C_PacketTypes; data: any } {
    try {
      // 解码包装消息
      const gameMessage = game.GameMessage.decode(buffer);
      
      // 获取对应的消息类型
      const packetType = this.protoToS2CMap.get(gameMessage.packetType);
      if (!packetType) {
        throw new Error(`Unknown packet type: ${gameMessage.packetType}`);
      }
      
      const MessageClass = this.s2cMessageMap.get(packetType);
      if (!MessageClass) {
        throw new Error(`Unsupported packet type: ${packetType}`);
      }
      
      // 解码内部消息
      const message = MessageClass.decode(gameMessage.data);
      
      // 转换为JSON格式
      const jsonData = this.convertToJsonFormat(message.toJSON(), packetType);
      
      return {
        packetType,
        data: jsonData
      };
    } catch (error) {
      console.error('Error decoding protobuf message:', error);
      throw error;
    }
  }

  /**
   * 将JSON格式转换为protobuf格式（字段命名转换）
   */
  private convertToProtobufFormat(data: any, packetType: S2C_PacketTypes): any {
    if (!data) return data;
    
    const converted = { ...data };
    
    // 根据消息类型进行特定的字段转换
    switch (packetType) {
      case S2C_PacketTypes.PlayerEnter:
      case S2C_PacketTypes.PlayerLeave:
        if (data.btcAddress) {
          converted.btc_address = data.btcAddress;
          delete converted.btcAddress;
        }
        if (data.sessionId) {
          converted.session_id = data.sessionId;
          delete converted.sessionId;
        }
        if (data.avatarData) {
          converted.avatar_data = this.convertAvatarData(data.avatarData);
          delete converted.avatarData;
        }
        break;
        
      case S2C_PacketTypes.PlayerPosition:
      case S2C_PacketTypes.PlayerAnimation:
      case S2C_PacketTypes.PlayerUpdate:
        if (data.btcAddress) {
          converted.btc_address = data.btcAddress;
          delete converted.btcAddress;
        }
        if (data.animationName) {
          converted.animation_name = data.animationName;
          delete converted.animationName;
        }
        break;
        
      case S2C_PacketTypes.PetPosition:
      case S2C_PacketTypes.PetAnimation:
        if (data.ownerBtcAddress) {
          converted.owner_btc_address = data.ownerBtcAddress;
          delete converted.ownerBtcAddress;
        }
        if (data.petId) {
          converted.pet_id = data.petId;
          delete converted.petId;
        }
        if (data.animationName) {
          converted.animation_name = data.animationName;
          delete converted.animationName;
        }
        break;
        
      case S2C_PacketTypes.ChatEnter:
      case S2C_PacketTypes.ChatLeave:
        if (data.chatId) {
          converted.chat_id = data.chatId;
          delete converted.chatId;
        }
        if (data.btcAddress) {
          converted.btc_address = data.btcAddress;
          delete converted.btcAddress;
        }
        break;
        
      case S2C_PacketTypes.ChatMessage:
        if (data.playerId) {
          converted.player_id = data.playerId;
          delete converted.playerId;
        }
        if (data.replyTo) {
          converted.reply_to = data.replyTo;
          delete converted.replyTo;
        }
        if (data.tabType) {
          converted.tab_type = data.tabType;
          delete converted.tabType;
        }
        break;
        
      case S2C_PacketTypes.RedPacketUpdate:
        if (data.packetId) {
          converted.packet_id = data.packetId;
          delete converted.packetId;
        }
        if (data.creatorAddress) {
          converted.creator_address = data.creatorAddress;
          delete converted.creatorAddress;
        }
        if (data.createdAt) {
          converted.created_at = data.createdAt;
          delete converted.createdAt;
        }
        if (data.expiresAt) {
          converted.expires_at = data.expiresAt;
          delete converted.expiresAt;
        }
        break;
    }
    
    return converted;
  }

  /**
   * 将protobuf格式转换为JSON格式（字段命名转换）
   */
  private convertToJsonFormat(data: any, packetType: S2C_PacketTypes): any {
    if (!data) return data;
    
    const converted = { ...data };
    
    // 根据消息类型进行特定的字段转换（反向转换）
    switch (packetType) {
      case S2C_PacketTypes.PlayerEnter:
      case S2C_PacketTypes.PlayerLeave:
        if (data.btc_address) {
          converted.btcAddress = data.btc_address;
          delete converted.btc_address;
        }
        if (data.session_id) {
          converted.sessionId = data.session_id;
          delete converted.session_id;
        }
        if (data.avatar_data) {
          converted.avatarData = this.convertAvatarDataFromProto(data.avatar_data);
          delete converted.avatar_data;
        }
        break;
        
      case S2C_PacketTypes.PlayerPosition:
      case S2C_PacketTypes.PlayerAnimation:
      case S2C_PacketTypes.PlayerUpdate:
        if (data.btc_address) {
          converted.btcAddress = data.btc_address;
          delete converted.btc_address;
        }
        if (data.animation_name) {
          converted.animationName = data.animation_name;
          delete converted.animation_name;
        }
        break;
        
      case S2C_PacketTypes.PetPosition:
      case S2C_PacketTypes.PetAnimation:
        if (data.owner_btc_address) {
          converted.ownerBtcAddress = data.owner_btc_address;
          delete converted.owner_btc_address;
        }
        if (data.pet_id) {
          converted.petId = data.pet_id;
          delete converted.pet_id;
        }
        if (data.animation_name) {
          converted.animationName = data.animation_name;
          delete converted.animation_name;
        }
        break;
        
      case S2C_PacketTypes.ChatEnter:
      case S2C_PacketTypes.ChatLeave:
        if (data.chat_id) {
          converted.chatId = data.chat_id;
          delete converted.chat_id;
        }
        if (data.btc_address) {
          converted.btcAddress = data.btc_address;
          delete converted.btc_address;
        }
        break;
        
      case S2C_PacketTypes.ChatMessage:
        if (data.player_id) {
          converted.playerId = data.player_id;
          delete converted.player_id;
        }
        if (data.reply_to) {
          converted.replyTo = data.reply_to;
          delete converted.reply_to;
        }
        if (data.tab_type) {
          converted.tabType = data.tab_type;
          delete converted.tab_type;
        }
        break;
        
      case S2C_PacketTypes.RedPacketUpdate:
        if (data.packet_id) {
          converted.packetId = data.packet_id;
          delete converted.packet_id;
        }
        if (data.creator_address) {
          converted.creatorAddress = data.creator_address;
          delete converted.creator_address;
        }
        if (data.created_at) {
          converted.createdAt = data.created_at;
          delete converted.created_at;
        }
        if (data.expires_at) {
          converted.expiresAt = data.expires_at;
          delete converted.expires_at;
        }
        break;
    }
    
    return converted;
  }

  /**
   * 转换头像数据到protobuf格式
   */
  private convertAvatarData(avatarData: any): any {
    if (!avatarData) return avatarData;
    
    return {
      shirt_id: avatarData.shirtId,
      shirt_texture_id: avatarData.shirtTextureId,
      shirt_color: avatarData.shirtColor,
      pants_id: avatarData.pantsId,
      shoes_id: avatarData.shoesId,
      hat_id: avatarData.hatId,
    };
  }

  /**
   * 从protobuf格式转换头像数据
   */
  private convertAvatarDataFromProto(avatarData: any): any {
    if (!avatarData) return avatarData;
    
    return {
      shirtId: avatarData.shirt_id,
      shirtTextureId: avatarData.shirt_texture_id,
      shirtColor: avatarData.shirt_color,
      pantsId: avatarData.pants_id,
      shoesId: avatarData.shoes_id,
      hatId: avatarData.hat_id,
    };
  }

  /**
   * 检查是否支持指定的消息类型
   */
  public isSupported(packetType: S2C_PacketTypes): boolean {
    return this.s2cMessageMap.has(packetType);
  }

  /**
   * 获取支持的消息类型列表
   */
  public getSupportedTypes(): S2C_PacketTypes[] {
    return Array.from(this.s2cMessageMap.keys());
  }
}

export default ProtobufHandler;
