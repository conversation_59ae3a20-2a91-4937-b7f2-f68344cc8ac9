/**
 * Protobuf配置管理器
 * 控制何时使用protobuf协议进行通讯
 */
export class ProtobufConfig {
  private static instance: ProtobufConfig;

  // 是否启用protobuf（默认开启）
  private enabled: boolean = true;

  // 支持protobuf的客户端列表（基于IP或用户标识）
  private supportedClients: Set<string> = new Set();

  // 强制使用protobuf的消息类型
  private forcedProtobufTypes: Set<number> = new Set();

  public static getInstance(): ProtobufConfig {
    if (!ProtobufConfig.instance) {
      ProtobufConfig.instance = new ProtobufConfig();
    }
    return ProtobufConfig.instance;
  }

  /**
   * 启用或禁用protobuf
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
    console.info(`Protobuf ${enabled ? 'enabled' : 'disabled'} globally`);
  }

  /**
   * 检查是否全局启用protobuf
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 添加支持protobuf的客户端
   * @param clientId 客户端标识（可以是IP、btcAddress等）
   */
  public addSupportedClient(clientId: string): void {
    this.supportedClients.add(clientId);
    console.info(`Client ${clientId} added to protobuf support list`);
  }

  /**
   * 移除支持protobuf的客户端
   * @param clientId 客户端标识
   */
  public removeSupportedClient(clientId: string): void {
    this.supportedClients.delete(clientId);
    console.info(`Client ${clientId} removed from protobuf support list`);
  }

  /**
   * 检查客户端是否支持protobuf
   * @param clientId 客户端标识
   */
  public isClientSupported(clientId: string): boolean {
    return this.supportedClients.has(clientId);
  }

  /**
   * 添加强制使用protobuf的消息类型
   * @param messageType 消息类型
   */
  public addForcedProtobufType(messageType: number): void {
    this.forcedProtobufTypes.add(messageType);
  }

  /**
   * 检查消息类型是否强制使用protobuf
   * @param messageType 消息类型
   */
  public isForcedProtobufType(messageType: number): boolean {
    return this.forcedProtobufTypes.has(messageType);
  }

  /**
   * 判断是否应该对特定客户端和消息类型使用protobuf
   * @param clientId 客户端标识
   * @param messageType 消息类型
   */
  public shouldUseProtobuf(clientId: string, messageType?: number): boolean {
    // 全局默认使用protobuf
    return this.enabled;
  }

  /**
   * 从环境变量加载配置（可选）
   */
  public loadFromEnv(): void {
    // 可以通过环境变量禁用protobuf（如果需要）
    if (process.env.DISABLE_PROTOBUF === 'true') {
      this.setEnabled(false);
      console.info('Protobuf disabled via environment variable');
    }

    // 其他环境变量配置保持不变，但不影响默认启用状态
    const supportedClients = process.env.PROTOBUF_SUPPORTED_CLIENTS;
    if (supportedClients) {
      supportedClients.split(',').forEach(clientId => {
        this.addSupportedClient(clientId.trim());
      });
    }

    const forcedTypes = process.env.PROTOBUF_FORCED_TYPES;
    if (forcedTypes) {
      forcedTypes.split(',').forEach(type => {
        const typeNum = parseInt(type.trim());
        if (!isNaN(typeNum)) {
          this.addForcedProtobufType(typeNum);
        }
      });
    }
  }

  /**
   * 获取当前配置状态
   */
  public getStatus(): {
    enabled: boolean;
    supportedClientsCount: number;
    forcedTypesCount: number;
  } {
    return {
      enabled: this.enabled,
      supportedClientsCount: this.supportedClients.size,
      forcedTypesCount: this.forcedProtobufTypes.size,
    };
  }

  /**
   * 重置所有配置
   */
  public reset(): void {
    this.enabled = false;
    this.supportedClients.clear();
    this.forcedProtobufTypes.clear();
    console.info('Protobuf configuration reset');
  }
}

export default ProtobufConfig;
