import * as protobuf from 'protobufjs';
import { game } from './generated/game_messages';
import { C2S_PacketTypes, ClientRequestTypes } from 'common/types';

/**
 * 客户端Protobuf消息处理器
 * 负责解析来自客户端的protobuf数据
 */
export class ClientProtobufHandler {
  private static instance: ClientProtobufHandler;
  
  // C2S消息类型映射：从枚举到protobuf消息类型
  private readonly c2sMessageMap = new Map<C2S_PacketTypes, any>([
    [C2S_PacketTypes.PlayerEnter, game.ClientPlayerEnter],
    [C2S_PacketTypes.PlayerLeave, null], // 无需额外数据
    [C2S_PacketTypes.PlayerPosition, game.ClientPlayerPosition],
    [C2S_PacketTypes.PlayerAnimation, game.ClientPlayerAnimation],
    [C2S_PacketTypes.PlayerUpdate, game.ClientPlayerUpdate],
    [C2S_PacketTypes.PetPosition, game.ClientPlayerPosition], // 复用位置数据结构
    [C2S_PacketTypes.PetAnimation, game.ClientPlayerAnimation], // 复用动画数据结构
    [C2S_PacketTypes.ChatEnter, null], // 无需额外数据
    [C2S_PacketTypes.ChatLeave, null], // 无需额外数据
    [C2S_PacketTypes.ChatMessage, game.ChatMessage],
  ]);

  // 客户端请求类型映射
  private readonly clientRequestMap = new Map<ClientRequestTypes, any>([
    [ClientRequestTypes.PickUpRedPacket, null], // 使用通用数据结构
  ]);

  public static getInstance(): ClientProtobufHandler {
    if (!ClientProtobufHandler.instance) {
      ClientProtobufHandler.instance = new ClientProtobufHandler();
    }
    return ClientProtobufHandler.instance;
  }

  /**
   * 解析客户端动作消息
   * @param buffer protobuf二进制数据
   * @returns 解析后的动作数据
   */
  public decodePlayerAction(buffer: Buffer): { pid: C2S_PacketTypes; data: any; btcAddress: string } {
    try {
      // 解码动作消息包装器
      const actionMessage = game.PlayerActionMessage.decode(buffer);
      
      const pid = actionMessage.pid as C2S_PacketTypes;
      const btcAddress = actionMessage.btcAddress;
      
      // 获取对应的消息类型
      const MessageClass = this.c2sMessageMap.get(pid);
      
      let data: any = {};
      
      if (MessageClass && actionMessage.actionData && actionMessage.actionData.length > 0) {
        // 解码内部数据
        const innerMessage = MessageClass.decode(actionMessage.actionData);
        data = this.convertToJsonFormat(innerMessage.toJSON(), pid);
      }
      
      return {
        pid,
        data,
        btcAddress
      };
    } catch (error) {
      console.error('Error decoding player action:', error);
      throw error;
    }
  }

  /**
   * 解析客户端请求消息
   * @param buffer protobuf二进制数据
   * @returns 解析后的请求数据
   */
  public decodePlayerRequest(buffer: Buffer): { pid: ClientRequestTypes; data: any; btcAddress: string } {
    try {
      // 解码请求消息包装器
      const requestMessage = game.PlayerRequestMessage.decode(buffer);
      
      const pid = requestMessage.pid as ClientRequestTypes;
      const btcAddress = requestMessage.btcAddress;
      
      // 对于请求消息，通常使用通用的JSON数据结构
      let data: any = {};
      
      if (requestMessage.requestData && requestMessage.requestData.length > 0) {
        // 尝试解析为JSON字符串
        try {
          const jsonString = Buffer.from(requestMessage.requestData).toString('utf8');
          data = JSON.parse(jsonString);
        } catch (jsonError) {
          console.warn('Failed to parse request data as JSON, using raw data');
          data = { rawData: requestMessage.requestData };
        }
      }
      
      return {
        pid,
        data,
        btcAddress
      };
    } catch (error) {
      console.error('Error decoding player request:', error);
      throw error;
    }
  }

  /**
   * 解析客户端聊天消息
   * @param buffer protobuf二进制数据
   * @returns 解析后的聊天数据
   */
  public decodePlayerChat(buffer: Buffer): { chatId: number; pid: C2S_PacketTypes; data: any; btcAddress: string } {
    try {
      // 解码聊天消息包装器
      const chatMessage = game.PlayerChatMessage.decode(buffer);
      
      const chatId = chatMessage.chatId;
      const pid = chatMessage.pid as C2S_PacketTypes;
      const btcAddress = chatMessage.btcAddress;
      
      let data: any = {};
      
      if (chatMessage.chatData && chatMessage.chatData.length > 0) {
        // 聊天消息通常是ChatMessage格式
        if (pid === C2S_PacketTypes.ChatMessage) {
          const innerMessage = game.ChatMessage.decode(chatMessage.chatData);
          data = this.convertChatMessageToJson(innerMessage.toJSON());
        }
      }
      
      return {
        chatId,
        pid,
        data,
        btcAddress
      };
    } catch (error) {
      console.error('Error decoding player chat:', error);
      throw error;
    }
  }

  /**
   * 将protobuf格式转换为JSON格式（字段命名转换）
   */
  private convertToJsonFormat(data: any, packetType: C2S_PacketTypes): any {
    if (!data) return data;
    
    const converted = { ...data };
    
    // 根据消息类型进行特定的字段转换
    switch (packetType) {
      case C2S_PacketTypes.PlayerEnter:
        if (data.avatar_data) {
          converted.avatarData = this.convertAvatarDataFromProto(data.avatar_data);
          delete converted.avatar_data;
        }
        break;
        
      case C2S_PacketTypes.PlayerPosition:
        // 位置数据保持原样，字段名已经是正确的
        break;
        
      case C2S_PacketTypes.PlayerAnimation:
        if (data.cur_animation) {
          converted.curAnimation = data.cur_animation;
          delete converted.cur_animation;
        }
        break;
        
      case C2S_PacketTypes.PlayerUpdate:
        if (data.item_id) {
          converted.itemId = data.item_id;
          delete converted.item_id;
        }
        if (data.pizza_count) {
          converted.pizzaCount = data.pizza_count;
          delete converted.pizza_count;
        }
        if (data.pet_id) {
          converted.petId = data.pet_id;
          delete converted.pet_id;
        }
        break;
    }
    
    return converted;
  }

  /**
   * 转换聊天消息到JSON格式
   */
  private convertChatMessageToJson(data: any): any {
    if (!data) return data;
    
    const converted = { ...data };
    
    if (data.player_id) {
      converted.playerId = data.player_id;
      delete converted.player_id;
    }
    if (data.reply_to) {
      converted.replyTo = data.reply_to;
      delete converted.reply_to;
    }
    if (data.tab_type) {
      converted.tabType = data.tab_type;
      delete converted.tab_type;
    }
    
    return converted;
  }

  /**
   * 从protobuf格式转换头像数据
   */
  private convertAvatarDataFromProto(avatarData: any): any {
    if (!avatarData) return avatarData;
    
    return {
      shirtId: avatarData.shirt_id,
      shirtTextureId: avatarData.shirt_texture_id,
      shirtColor: avatarData.shirt_color,
      pantsId: avatarData.pants_id,
      shoesId: avatarData.shoes_id,
      hatId: avatarData.hat_id,
    };
  }

  /**
   * 检查是否支持指定的消息类型
   */
  public isActionSupported(packetType: C2S_PacketTypes): boolean {
    return this.c2sMessageMap.has(packetType);
  }

  /**
   * 检查是否支持指定的请求类型
   */
  public isRequestSupported(requestType: ClientRequestTypes): boolean {
    return this.clientRequestMap.has(requestType);
  }

  /**
   * 获取支持的动作类型列表
   */
  public getSupportedActionTypes(): C2S_PacketTypes[] {
    return Array.from(this.c2sMessageMap.keys());
  }

  /**
   * 获取支持的请求类型列表
   */
  public getSupportedRequestTypes(): ClientRequestTypes[] {
    return Array.from(this.clientRequestMap.keys());
  }

  /**
   * 尝试检测数据是否为protobuf格式
   * @param data 接收到的数据
   * @returns 是否为protobuf格式
   */
  public isProtobufData(data: any): boolean {
    // 如果数据是Buffer或Uint8Array，可能是protobuf
    if (Buffer.isBuffer(data) || data instanceof Uint8Array) {
      return true;
    }
    
    // 如果数据是对象且包含特定的protobuf标识，也可能是protobuf
    if (typeof data === 'object' && data !== null) {
      // 检查是否有protobuf的特征字段
      return false; // 对于JSON对象，返回false
    }
    
    return false;
  }
}

export default ClientProtobufHandler;
