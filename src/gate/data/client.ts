import {
  AvatarData,
  ClusterNodeType,
  NodeEvents,
  Player,
  S2C_PacketTypes,
  SocketEvents
} from "common/types";
import {ClusterManager} from "common/cluster-manager";
import {<PERSON>bufHand<PERSON>} from "proto/protobuf-handler";
import {ProtobufConfig} from "proto/protobuf-config";


type PingData = {
  pingTime: number
  totalPing: number
  maxPing: number
  minPing: number
}

export class GatewayClient {
  private btcAddress: string = '';
  private socket: any;
  private cluster: ClusterManager;
  private gameNodeId: string = '';
  private joinTime: number = 0;
  private gateway_id: string;
  private ipAddress: string = '';
  private sessionId: string = '';
  private chatNodeMap: Map<number, string> = new Map<number, string>()
  private pingData: PingData = {
    pingTime: 0,
    totalPing: 0,
    maxPing: 0,
    minPing: 99999
  }

  constructor(socket: any, gateway_id: string, cluster: ClusterManager) {
    this.socket = socket;
    this.cluster = cluster;
    this.gateway_id = gateway_id
    this.joinTime = Date.now()
    this.ipAddress = socket.handshake.address;
  }

  login(btcAddress: string, sessionId: string) {
    console.info('login', btcAddress)
    this.btcAddress = btcAddress
    this.sessionId = sessionId
    this.listenRoomMessage(btcAddress)
  }

  ping(lastPing: number) {
    if (lastPing > 0) {
      this.pingData.pingTime++;
      this.pingData.totalPing += lastPing
      this.pingData.maxPing = Math.max(this.pingData.maxPing, lastPing)
      this.pingData.minPing = Math.min(this.pingData.minPing, lastPing)
    }

    return Date.now()
  }

  getPingData() {
    return {
      ...this.pingData,
      avgPing: this.pingData.pingTime > 0 ? Math.round(this.pingData.totalPing / this.pingData.pingTime) : 0
    };
  }

  getAddress() {
    return this.btcAddress
  }

  getInfo() {
    return {
      btcAddress: this.btcAddress,
      sessionId: this.sessionId,
      gateway_id: this.gateway_id,
      joinTime: this.joinTime,
      ipAddress: this.ipAddress
    } as Player
  }

  setGameNodeId(gameNodeId: string) {
    // 设置游戏房间ID
    this.gameNodeId = gameNodeId;
  }

  async sendToGameRoom(method: string, message: any) {
    if (this.gameNodeId.length === 0) {
      this.socket.emit(SocketEvents.LEAVE_ROOM, {});
      // console.error('sendToGameRoom Player is not in a game room', method, message)
      return
    }
    // 发送消息到指定房间
    await this.cluster.send(this.gameNodeId, method, message)
  }

  async callToGameRoom(method: string, message: any) {
    if (this.gameNodeId.length === 0) {
      console.error('callToGameRoom Player is not in a game room', method, message)
      return
    }
    // 发送消息到指定房间
    return await this.cluster.call(this.gameNodeId, method, message)
  }

  notice(pid: S2C_PacketTypes, data: any, isWebNotice = false) {
    this.socket.emit(isWebNotice ? SocketEvents.WEB_NOTICE : SocketEvents.PLAYER_NOTICE, {
      ...data,
      pid
    });
  }

  kick(): string {
    this.socket.emit(SocketEvents.PLAYER_KICK, {});
    return this.socket.id
  }

  errorMsg(message: string) {
    this.socket.emit(SocketEvents.ERROR, message);
  }

  listenRoomMessage(btcAddress: string) {

    const roomSocketEvents = [
      SocketEvents.JOIN_ROOM,
      SocketEvents.LEAVE_ROOM,
      SocketEvents.PLAYER_ACTION,
      SocketEvents.PLAYER_REQUEST,
      SocketEvents.PLAYER_CHAT,
    ];

    async function handlePlayerEvent(client: GatewayClient, eventType: string, request: any, callback: any) {
      try {
        if (client.getAddress().length === 0) {
          // client.errorMsg('login first plz');
          return
        }
        switch (eventType) {
          // 玩家匹配
          case SocketEvents.JOIN_ROOM:
            const {mapId, mapIndex} = request;
            try {
              const targetId = await client.cluster.getNodesIdByType(ClusterNodeType.ROOM_NODE);
              if (targetId) {
                // 通知相应房间节点有新玩家加入（通过集群消息）
                const gameNodeId = await client.cluster.call(targetId, NodeEvents.FIND_GAME_ROOM, {
                  btcAddress, mapId, mapIndex
                });
                await client.enterGameRoom(gameNodeId)
              } else {
                client.errorMsg('room node not found');
              }
            } catch (error) {
              client.errorMsg('error catch' + String(error));
            }
            break;
          // 玩家离开房间
          case SocketEvents.LEAVE_ROOM:
            client.leaveGameRoom().then()
            break;
          // 玩家动作
          case SocketEvents.PLAYER_ACTION:
            const {pid} = request;
            client.sendToGameRoom(SocketEvents.PLAYER_ACTION, {
              game_action: request,
              pid,
              btcAddress: client.btcAddress
            }).then()
            break;
          // 玩家动作
          case SocketEvents.PLAYER_REQUEST:
            const resp = await client.callToGameRoom(SocketEvents.PLAYER_REQUEST, {
              ...request,
              btcAddress: client.btcAddress
            })
            if (callback) {
              callback(resp)
            }
            break;
          // 玩家聊天
          case SocketEvents.PLAYER_CHAT:
            const {chatId, message} = request;
            client.sendChatRoom(chatId, message).then()
            break;
        }
      } catch (error) {
        console.error('Error handlePlayerEvent:', eventType, request, error);
      }
    }

    roomSocketEvents.forEach((eventType) => {
      this.socket.on(eventType, async (request: any, callback: any) => {
        handlePlayerEvent(this, eventType, request, callback).then();
      });
    })
  }

  async sendChatRoom(chatId: number, message: any) {
    console.log('sendChatRoom', chatId, message)
    let chatNodeId = await this.getChatRoomId(chatId)
    if (chatNodeId.length === 0)
      return
    // 发送消息到指定房间
    const {pid} = message;
    await this.cluster.send(chatNodeId, SocketEvents.PLAYER_CHAT, {
      game_action: message,
      pid,
      btcAddress: this.btcAddress
    })
  }

  async enterGameRoom(gameNodeId: string) {
    if (this.gameNodeId.length > 0) {
      const result = await this.leaveGameRoom()
      if (!result) {
        console.error('leaveGameRoom failed')
        // return 暂时不考虑终端流程 ，以后再改
      }
    }
    if (gameNodeId && gameNodeId.length > 0) {
      //需要先请求加入房间，成功后才能setGameNodeId，所以这里不能直接用callToGameRoom
      const roomData: {
        mode: string,
        modeIndex: number
      } = await this.cluster.call(gameNodeId, SocketEvents.PLAYER_JOINED, this.getInfo());
      if (roomData.mode && roomData.mode.length > 0) {
        this.setGameNodeId(gameNodeId);
        this.socket.emit(SocketEvents.MATCH_FOUND, roomData);
        // 通知玩家匹配成功
      } else {
        // 通知玩家匹配失败
        this.errorMsg('room is full');
      }
    } else {
      // 通知玩家匹配失败
      this.errorMsg('not found room');
    }
  }

  async getChatRoomId(chatId: number) {
    let chatNodeId = this.gameNodeId
    if (chatId > 1000) {
      chatNodeId = this.chatNodeMap.get(chatId) || ''
      if (chatNodeId.length === 0) {
        const targetId = await this.cluster.getNodesIdByType(ClusterNodeType.ROOM_NODE);
        if (targetId) {
          // 通知相应房间节点有新玩家加入（通过集群消息）
          chatNodeId = await this.cluster.call(targetId, NodeEvents.FIND_CHAT_ROOM, {
            btcAddress: this.btcAddress, chatId
          });
          if (chatNodeId) {
            //需要先请求加入房间，成功后才能setGameNodeId，所以这里不能直接用callToGameRoom
            const roomData: {
              mode: string,
              modeIndex: number
            } = await this.cluster.call(chatNodeId, SocketEvents.PLAYER_JOINED, this.getInfo());
            if (roomData.mode && roomData.mode.length > 0) {
              this.chatNodeMap.set(chatId, chatNodeId)
              // 通知玩家匹配成功
            } else {
              // 通知玩家匹配失败
              this.errorMsg('room is full');
            }
          }
        }
      }
    }
    return chatNodeId
  }

  async leaveChatRoom() {
    this.chatNodeMap.forEach((chatNodeId) => {
      this.cluster.call(chatNodeId, SocketEvents.PLAYER_LEFT, {
        btcAddress: this.btcAddress
      }).then()
    })

    this.chatNodeMap.clear()
  }

  async leaveGameRoom() {
    if (this.gameNodeId.length > 0) {
      const success = await this.callToGameRoom(SocketEvents.PLAYER_LEFT, {
        btcAddress: this.btcAddress
      })
      this.setGameNodeId('')
      return success
    }
    return true
  }

  destroy() {
    this.leaveGameRoom().then()
    this.leaveChatRoom().then()
  }

}