// src/gateway-server.ts
import {Server} from 'socket.io';
import {createAdapter} from '@socket.io/redis-adapter';
import {Redis} from 'ioredis';
import {
  ClusterNodeType,
  NodeEvents,
  SocketEvents
} from 'common/types';
import {ClusterManager} from 'common/cluster-manager';
import * as dotenv from 'dotenv';
import {GatewayClient} from "gate/data/client";
import {ProtobufConfig} from "proto/protobuf-config";

// 加载环境变量
dotenv.config();

export class GameGateway {
  private io: Server;
  private id: string;
  private cluster: ClusterManager;
  private redisPub: Redis;
  private redisSub: Redis;
  private clients: Map<string, GatewayClient> = new Map();

  private clientsHistory: { time: number, count: number }[] = []
  private maxClientCount = 0
  private lastClientCount = 0

  constructor(clusterManager: ClusterManager) {
    this.cluster = clusterManager;
    this.id = this.cluster.getNodeId();
    this.redisPub = new Redis(process.env.REDIS_URL!);
    this.redisSub = new Redis(process.env.REDIS_URL!);

    this.io = new Server({
      adapter: createAdapter(this.redisPub, this.redisSub),
      cors: {origin: '*'}
    });

    this.clientsHistory.push({
      time: Date.now(),
      count: 0
    })

    // 设置事件处理器
    this.setupEventHandlers();
    this.startNodeHealthCheck();
    this.setupListeners();
    this.nodeLoop().then();
  }

  private checkClientCount() {
    const count = this.getClientCount()
    if (count != this.lastClientCount) {
      this.lastClientCount = count
      this.clientsHistory.push({
        time: Date.now(),
        count: count
      })
    }
  }

  //检查重复登录
  private async checkDoubleLogin(btcAddress: string, kick: boolean = false) {
    const client = this.clients.get(btcAddress);
    if (client) {
      if (kick) {
        const playerId = client.kick();
        await this.handlePlayerDisconnect(playerId)
        return true
      }
      return false
    }
    return true;
  }

  private getClientCount() {
    let count = 0
    // 遍历所有客户端，只收集已登录的用户
    this.clients.forEach((client, key) => {
      // 只包含已登录的用户（有btcAddress的）
      if (client.getAddress() && client.getAddress().length > 0) {
        if (key != client.getAddress()) {
          count++;
        }
      }
    });
    return count
  }

  private setupEventHandlers() {

    this.io.on(SocketEvents.CONNECT, (socket) => {
      const ipAddress = socket.handshake.address;
      console.info(`Client connected: ${socket.id}, IP: ${ipAddress}`);
      const client = new GatewayClient(socket, this.id, this.cluster);
      this.clients.set(socket.id, client);

      // 玩家登陆信息
      socket.on(SocketEvents.PLAYER_LOGIN, async (request, callback) => {
        try {
          const {btcAddress, sessionId} = request;
          if (await this.checkDoubleLogin(btcAddress, true)) {
            client.login(btcAddress, sessionId)
            this.clients.set(btcAddress, client);
            if (callback)
              callback(true)
            const count = this.getClientCount()
            if (this.maxClientCount < count) {
              this.maxClientCount = count
              this.checkClientCount()
            }
            return
          }
          if (callback)
            callback(false)
        } catch (error) {
          console.error('error catch SocketEvents.CONNECT' + String(error));
        }
      });

      socket.on(SocketEvents.GET_ROOM_LIST, async (request, callback) => {
        //查询房间信息
        try {
          const targetId = await this.cluster.getNodesIdByType(ClusterNodeType.ROOM_NODE);
          if (targetId) {
            // 通知相应房间节点有新玩家加入（通过集群消息）
            const roomInfos = await this.cluster.call(targetId, NodeEvents.GET_ROOM_LIST, request);
            callback(roomInfos)
          } else {
            //房间已满
            client.errorMsg('room node not found');
          }
        } catch (error) {
          console.error('error catch' + String(error));
        }
      })

      // 玩家登陆信息
      socket.on(SocketEvents.PLAYER_HEARTBEAT, async (request, callback) => {
        try {
          const {lastPing} = request;
          callback(client.ping(lastPing))
        } catch (error) {
          console.error('error catch SocketEvents.PLAYER_HEARTBEAT' + String(error));
        }
      });

      // 断开连接
      socket.on(SocketEvents.DISCONNECT, () => {
        try {
          console.info(`Client disconnected: ${socket.id}`);
          this.handlePlayerDisconnect(socket.id).then();
        } catch (error) {
          console.error('error catch SocketEvents.DISCONNECT' + String(error));
        }
      });
    });
  }

  private setupListeners() {
    // 注册集群事件处理
    this.cluster.registerRPCHandler(SocketEvents.PLAYER_NOTICE, async (notice) => {
      const {btcAddress, pid, data} = notice;
      const client = this.clients.get(btcAddress);
      if (client) {
        client.notice(pid, data)
      } else {
        console.error('client not found', btcAddress)
      }
    });

    // 添加获取所有用户信息的RPC处理器
    this.cluster.registerRPCHandler('GET_GATEWAY_CLIENTS', async () => {
      // 定义一个更宽松的类型
      interface ClientInfo {
        id: string;
        btcAddress: string;
        gateway_id?: string;
        avatarData?: any;
        joinTime?: number;
        pingData?: any;

        [key: string]: any; // 允许任意其他属性
      }

      const clientsArray: ClientInfo[] = [];

      // 遍历所有客户端，只收集已登录的用户
      this.clients.forEach((client, key) => {
        // 只包含已登录的用户（有btcAddress的）
        if (client.getAddress() && client.getAddress().length > 0) {
          if (key != client.getAddress()) {
            clientsArray.push({
              id: key,
              ...client.getInfo(),
              pingData: client.getPingData()
            });
          }
        }
      });

      return clientsArray;
    });

    // 添加获取所有用户信息的RPC处理器
    this.cluster.registerRPCHandler('GET_GATEWAY_HISTORY', async () => {
      return {
        maxCount: this.maxClientCount,
        history: this.clientsHistory
      };
    });

    // Web节点通知
    this.cluster.registerRPCHandler(NodeEvents.WEB_NOTICE_PLAYER, async (noticeList: any[]) => {
      noticeList.forEach((notice) => {
        const {targetAddress, pid, data} = notice;
        const client = this.clients.get(targetAddress);
        if (client) {
          client.notice(pid, {data}, true)
        } else {
          console.error('client not found', targetAddress)
        }
      });
    });
  }

  private async handlePlayerDisconnect(playerId: string) {
    try {
      const client = this.clients.get(playerId);
      console.info(`handlePlayerDisconnect`, playerId, client?.getInfo());
      if (client) {
        client.destroy();
        this.clients.delete(playerId);
        this.clients.delete(client.getAddress());
      }
      // 通知处理该房间的房间节点（使用集群消息）
    } catch (error) {
      console.error('Error handling player disconnect:', error);
    }
  }

  private startNodeHealthCheck() {
    // 定期检查节点健康状态
    setInterval(() => {
      const now = Date.now();
    }, 10000);
  }

  public start(port: number) {
    this.io.listen(port);
    console.info(`Gateway running on port ${port}`);
  }


  private async nodeLoop() {
    setInterval(() => {
      this.checkClientCount()
    }, 5 * 60 * 1000);
  }

}
