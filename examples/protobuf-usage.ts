/**
 * Protobuf使用示例
 * 展示如何在项目中使用protobuf功能
 */

import { ProtobufHandler } from '../src/proto/protobuf-handler';
import { ProtobufConfig } from '../src/proto/protobuf-config';
import { S2C_PacketTypes } from '../src/common/types';

// 示例1：基本配置
function basicConfiguration() {
  console.log('=== 基本配置示例 ===');
  
  const config = ProtobufConfig.getInstance();
  
  // 启用protobuf
  config.setEnabled(true);
  console.log('Protobuf已启用');
  
  // 添加支持protobuf的客户端
  config.addSupportedClient('*************');
  config.addSupportedClient('**********************************');
  console.log('已添加支持protobuf的客户端');
  
  // 强制特定消息类型使用protobuf
  config.addForcedProtobufType(S2C_PacketTypes.PlayerPosition);
  config.addForcedProtobufType(S2C_PacketTypes.PlayerAnimation);
  console.log('已设置强制使用protobuf的消息类型');
  
  // 检查配置状态
  const status = config.getStatus();
  console.log('配置状态:', status);
}

// 示例2：消息编码和解码
function messageEncodingExample() {
  console.log('\n=== 消息编码解码示例 ===');
  
  const handler = ProtobufHandler.getInstance();
  
  // 示例数据：玩家位置信息
  const playerPositionData = {
    btcAddress: '**********************************',
    x: 100.5,
    y: 200.0,
    z: 150.3,
    rotation_x: 0.0,
    rotation_y: 0.707,
    rotation_z: 0.0,
    rotation_w: 0.707
  };
  
  try {
    // 编码为protobuf
    const encodedData = handler.encodeMessage(S2C_PacketTypes.PlayerPosition, playerPositionData);
    console.log(`编码后的数据大小: ${encodedData.length} bytes`);
    
    // 解码回JSON
    const decodedData = handler.decodeMessage(encodedData);
    console.log('解码后的数据:', decodedData);
    
    // 比较原始数据和解码数据
    console.log('数据完整性检查:', 
      decodedData.data.btcAddress === playerPositionData.btcAddress ? '✅ 通过' : '❌ 失败'
    );
    
  } catch (error) {
    console.error('编码/解码错误:', error);
  }
}

// 示例3：客户端支持检测
function clientSupportExample() {
  console.log('\n=== 客户端支持检测示例 ===');
  
  const config = ProtobufConfig.getInstance();
  
  // 模拟不同的客户端
  const clients = [
    { id: '*************', name: '支持protobuf的客户端' },
    { id: '*************', name: '不支持protobuf的客户端' },
    { id: '**********************************', name: '已注册的BTC地址' }
  ];
  
  clients.forEach(client => {
    const shouldUse = config.shouldUseProtobuf(client.id, S2C_PacketTypes.PlayerPosition);
    console.log(`${client.name} (${client.id}): ${shouldUse ? '使用protobuf' : '使用JSON'}`);
  });
}

// 示例4：性能对比
function performanceComparison() {
  console.log('\n=== 性能对比示例 ===');
  
  const handler = ProtobufHandler.getInstance();
  
  // 创建测试数据
  const testData = {
    btcAddress: '**********************************',
    x: 100.5,
    y: 200.0,
    z: 150.3,
    rotation_x: 0.0,
    rotation_y: 0.707,
    rotation_z: 0.0,
    rotation_w: 0.707
  };
  
  const iterations = 1000;
  
  // JSON序列化性能测试
  console.time('JSON序列化');
  for (let i = 0; i < iterations; i++) {
    JSON.stringify({ ...testData, pid: S2C_PacketTypes.PlayerPosition });
  }
  console.timeEnd('JSON序列化');
  
  // Protobuf编码性能测试
  console.time('Protobuf编码');
  for (let i = 0; i < iterations; i++) {
    try {
      handler.encodeMessage(S2C_PacketTypes.PlayerPosition, testData);
    } catch (error) {
      // 忽略错误，继续测试
    }
  }
  console.timeEnd('Protobuf编码');
  
  // 数据大小对比
  const jsonSize = Buffer.from(JSON.stringify({ ...testData, pid: S2C_PacketTypes.PlayerPosition })).length;
  let protobufSize = 0;
  
  try {
    const protobufData = handler.encodeMessage(S2C_PacketTypes.PlayerPosition, testData);
    protobufSize = protobufData.length;
  } catch (error) {
    console.error('Protobuf编码失败:', error);
  }
  
  console.log(`JSON数据大小: ${jsonSize} bytes`);
  console.log(`Protobuf数据大小: ${protobufSize} bytes`);
  
  if (protobufSize > 0) {
    const savings = ((jsonSize - protobufSize) / jsonSize * 100).toFixed(1);
    console.log(`数据压缩率: ${savings}%`);
  }
}

// 示例5：错误处理
function errorHandlingExample() {
  console.log('\n=== 错误处理示例 ===');
  
  const handler = ProtobufHandler.getInstance();
  
  // 测试不支持的消息类型
  try {
    // 假设999是不支持的消息类型
    handler.encodeMessage(999 as S2C_PacketTypes, { test: 'data' });
  } catch (error) {
    console.log('✅ 正确捕获不支持的消息类型错误:', error);
  }
  
  // 测试无效数据
  try {
    handler.encodeMessage(S2C_PacketTypes.PlayerPosition, null);
  } catch (error) {
    console.log('✅ 正确捕获无效数据错误:', error);
  }
  
  // 测试无效的protobuf数据解码
  try {
    const invalidBuffer = Buffer.from('invalid protobuf data');
    handler.decodeMessage(invalidBuffer);
  } catch (error) {
    console.log('✅ 正确捕获解码错误:', error);
  }
}

// 运行所有示例
function runAllExamples() {
  console.log('🚀 开始运行Protobuf使用示例...\n');
  
  try {
    basicConfiguration();
    messageEncodingExample();
    clientSupportExample();
    performanceComparison();
    errorHandlingExample();
    
    console.log('\n✅ 所有示例运行完成！');
  } catch (error) {
    console.error('\n❌ 示例运行出错:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runAllExamples();
}

export {
  basicConfiguration,
  messageEncodingExample,
  clientSupportExample,
  performanceComparison,
  errorHandlingExample,
  runAllExamples
};
